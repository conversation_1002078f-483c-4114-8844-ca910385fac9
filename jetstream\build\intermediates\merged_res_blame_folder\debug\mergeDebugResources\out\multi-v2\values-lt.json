{"logs": [{"outputFile": "com.google.jetstream-mergeDebugResources-66:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67a3f5723098babf64b890147a256c9e\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "9567,9655", "endColumns": "87,87", "endOffsets": "9650,9738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76081e3f0d6e0e3bdde7fa4601d62d99\\transformed\\core-1.15.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "2,3,4,5,6,7,8,90", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,313,412,515,626,736,9181", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "198,308,407,510,621,731,851,9277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\056f65275b6119073c278699d563b9b6\\transformed\\media3-exoplayer-1.6.0-beta01\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1331,1412,1476,1543,1611,1692,1766,1863,1958", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "1407,1471,1538,1606,1687,1761,1858,1953,2028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3ff6a694fcbdd48a9d50b8a2d9e5cf55\\transformed\\material3-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,411,529,630,724,835,967,1083,1227,1311,1410,1506,1605,1730,1848,1952,2091,2226,2365,2561,2691,2809,2935,3062,3159,3260,3382,3511,3609,3712,3819,3957,4105,4214,4318,4402,4498,4594,4709,4797,4887,4998,5078,5165,5265,5374,5470,5569,5657,5768,5864,5964,6102,6186,6289", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "169,289,406,524,625,719,830,962,1078,1222,1306,1405,1501,1600,1725,1843,1947,2086,2221,2360,2556,2686,2804,2930,3057,3154,3255,3377,3506,3604,3707,3814,3952,4100,4209,4313,4397,4493,4589,4704,4792,4882,4993,5073,5160,5260,5369,5465,5564,5652,5763,5859,5959,6097,6181,6284,6381"}, "to": {"startLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2201,2320,2440,2557,2675,2776,2870,2981,3113,3229,3373,3457,3556,3652,3751,3876,3994,4098,4237,4372,4511,4707,4837,4955,5081,5208,5305,5406,5528,5657,5755,5858,5965,6103,6251,6360,6464,6548,6644,6740,6855,6943,7033,7144,7224,7311,7411,7520,7616,7715,7803,7914,8010,8110,8248,8332,8435", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "2315,2435,2552,2670,2771,2865,2976,3108,3224,3368,3452,3551,3647,3746,3871,3989,4093,4232,4367,4506,4702,4832,4950,5076,5203,5300,5401,5523,5652,5750,5853,5960,6098,6246,6355,6459,6543,6639,6735,6850,6938,7028,7139,7219,7306,7406,7515,7611,7710,7798,7909,8005,8105,8243,8327,8430,8527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\598ef20c77cd1b7c623fcd4d2eedf4e3\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,1005,1093,1168,1245,1322,1397,1477,1560", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,1000,1088,1163,1240,1317,1392,1472,1555,1677"}, "to": {"startLines": "9,10,11,12,13,23,24,82,83,84,85,86,87,88,89,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "856,949,1033,1131,1236,2033,2110,8532,8619,8703,8789,8877,8952,9029,9106,9282,9362,9445", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "944,1028,1126,1231,1326,2105,2196,8614,8698,8784,8872,8947,9024,9101,9176,9357,9440,9562"}}]}]}