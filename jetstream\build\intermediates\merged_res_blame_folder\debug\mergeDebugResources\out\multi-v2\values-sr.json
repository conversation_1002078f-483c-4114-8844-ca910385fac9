{"logs": [{"outputFile": "com.google.jetstream-mergeDebugResources-66:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\598ef20c77cd1b7c623fcd4d2eedf4e3\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,998,1083,1156,1233,1311,1387,1466,1536", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,993,1078,1151,1228,1306,1382,1461,1531,1649"}, "to": {"startLines": "9,10,11,12,13,23,24,82,83,84,85,86,87,88,89,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,928,1015,1112,1213,1963,2040,8400,8492,8577,8657,8742,8815,8892,8970,9147,9226,9296", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "923,1010,1107,1208,1294,2035,2126,8487,8572,8652,8737,8810,8887,8965,9041,9221,9291,9409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\056f65275b6119073c278699d563b9b6\\transformed\\media3-exoplayer-1.6.0-beta01\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1299,1373,1434,1499,1570,1648,1720,1807,1890", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "1368,1429,1494,1565,1643,1715,1802,1885,1958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67a3f5723098babf64b890147a256c9e\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,89", "endOffsets": "138,228"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "9414,9502", "endColumns": "87,89", "endOffsets": "9497,9587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3ff6a694fcbdd48a9d50b8a2d9e5cf55\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2131,2247,2363,2490,2606,2704,2798,2909,3045,3164,3306,3391,3491,3586,3684,3800,3925,4030,4171,4311,4444,4624,4749,4869,4994,5116,5212,5310,5427,5557,5657,5759,5868,6010,6159,6268,6371,6448,6546,6644,6753,6842,6928,7035,7115,7198,7295,7398,7491,7589,7676,7784,7881,7983,8116,8196,8303", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "2242,2358,2485,2601,2699,2793,2904,3040,3159,3301,3386,3486,3581,3679,3795,3920,4025,4166,4306,4439,4619,4744,4864,4989,5111,5207,5305,5422,5552,5652,5754,5863,6005,6154,6263,6366,6443,6541,6639,6748,6837,6923,7030,7110,7193,7290,7393,7486,7584,7671,7779,7876,7978,8111,8191,8298,8395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76081e3f0d6e0e3bdde7fa4601d62d99\\transformed\\core-1.15.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,90", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,9046", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,9142"}}]}]}