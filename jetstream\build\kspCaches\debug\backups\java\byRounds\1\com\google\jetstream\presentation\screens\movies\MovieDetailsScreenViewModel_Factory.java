package com.google.jetstream.presentation.screens.movies;

import androidx.lifecycle.SavedStateHandle;
import com.google.jetstream.data.database.dao.ScrapedItemDao;
import com.google.jetstream.data.repositories.MovieRepository;
import com.google.jetstream.data.repositories.RecentlyWatchedRepository;
import com.google.jetstream.data.repositories.ScrapedMoviesStore;
import com.google.jetstream.data.repositories.ScrapedTvStore;
import com.google.jetstream.data.services.EpisodeMatchingService;
import com.google.jetstream.data.services.TvPlaybackService;
import com.google.jetstream.data.webdav.WebDavService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MovieDetailsScreenViewModel_Factory implements Factory<MovieDetailsScreenViewModel> {
  private final Provider<SavedStateHandle> savedStateHandleProvider;

  private final Provider<MovieRepository> repositoryProvider;

  private final Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider;

  private final Provider<ScrapedTvStore> scrapedTvStoreProvider;

  private final Provider<WebDavService> webDavServiceProvider;

  private final Provider<ScrapedItemDao> scrapedItemDaoProvider;

  private final Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider;

  private final Provider<EpisodeMatchingService> episodeMatchingServiceProvider;

  private final Provider<TvPlaybackService> tvPlaybackServiceProvider;

  public MovieDetailsScreenViewModel_Factory(Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<MovieRepository> repositoryProvider,
      Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      Provider<ScrapedTvStore> scrapedTvStoreProvider,
      Provider<WebDavService> webDavServiceProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider,
      Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider,
      Provider<EpisodeMatchingService> episodeMatchingServiceProvider,
      Provider<TvPlaybackService> tvPlaybackServiceProvider) {
    this.savedStateHandleProvider = savedStateHandleProvider;
    this.repositoryProvider = repositoryProvider;
    this.scrapedMoviesStoreProvider = scrapedMoviesStoreProvider;
    this.scrapedTvStoreProvider = scrapedTvStoreProvider;
    this.webDavServiceProvider = webDavServiceProvider;
    this.scrapedItemDaoProvider = scrapedItemDaoProvider;
    this.recentlyWatchedRepositoryProvider = recentlyWatchedRepositoryProvider;
    this.episodeMatchingServiceProvider = episodeMatchingServiceProvider;
    this.tvPlaybackServiceProvider = tvPlaybackServiceProvider;
  }

  @Override
  public MovieDetailsScreenViewModel get() {
    return newInstance(savedStateHandleProvider.get(), repositoryProvider.get(), scrapedMoviesStoreProvider.get(), scrapedTvStoreProvider.get(), webDavServiceProvider.get(), scrapedItemDaoProvider.get(), recentlyWatchedRepositoryProvider.get(), episodeMatchingServiceProvider.get(), tvPlaybackServiceProvider.get());
  }

  public static MovieDetailsScreenViewModel_Factory create(
      javax.inject.Provider<SavedStateHandle> savedStateHandleProvider,
      javax.inject.Provider<MovieRepository> repositoryProvider,
      javax.inject.Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      javax.inject.Provider<ScrapedTvStore> scrapedTvStoreProvider,
      javax.inject.Provider<WebDavService> webDavServiceProvider,
      javax.inject.Provider<ScrapedItemDao> scrapedItemDaoProvider,
      javax.inject.Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider,
      javax.inject.Provider<EpisodeMatchingService> episodeMatchingServiceProvider,
      javax.inject.Provider<TvPlaybackService> tvPlaybackServiceProvider) {
    return new MovieDetailsScreenViewModel_Factory(Providers.asDaggerProvider(savedStateHandleProvider), Providers.asDaggerProvider(repositoryProvider), Providers.asDaggerProvider(scrapedMoviesStoreProvider), Providers.asDaggerProvider(scrapedTvStoreProvider), Providers.asDaggerProvider(webDavServiceProvider), Providers.asDaggerProvider(scrapedItemDaoProvider), Providers.asDaggerProvider(recentlyWatchedRepositoryProvider), Providers.asDaggerProvider(episodeMatchingServiceProvider), Providers.asDaggerProvider(tvPlaybackServiceProvider));
  }

  public static MovieDetailsScreenViewModel_Factory create(
      Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<MovieRepository> repositoryProvider,
      Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      Provider<ScrapedTvStore> scrapedTvStoreProvider,
      Provider<WebDavService> webDavServiceProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider,
      Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider,
      Provider<EpisodeMatchingService> episodeMatchingServiceProvider,
      Provider<TvPlaybackService> tvPlaybackServiceProvider) {
    return new MovieDetailsScreenViewModel_Factory(savedStateHandleProvider, repositoryProvider, scrapedMoviesStoreProvider, scrapedTvStoreProvider, webDavServiceProvider, scrapedItemDaoProvider, recentlyWatchedRepositoryProvider, episodeMatchingServiceProvider, tvPlaybackServiceProvider);
  }

  public static MovieDetailsScreenViewModel newInstance(SavedStateHandle savedStateHandle,
      MovieRepository repository, ScrapedMoviesStore scrapedMoviesStore,
      ScrapedTvStore scrapedTvStore, WebDavService webDavService, ScrapedItemDao scrapedItemDao,
      RecentlyWatchedRepository recentlyWatchedRepository,
      EpisodeMatchingService episodeMatchingService, TvPlaybackService tvPlaybackService) {
    return new MovieDetailsScreenViewModel(savedStateHandle, repository, scrapedMoviesStore, scrapedTvStore, webDavService, scrapedItemDao, recentlyWatchedRepository, episodeMatchingService, tvPlaybackService);
  }
}
