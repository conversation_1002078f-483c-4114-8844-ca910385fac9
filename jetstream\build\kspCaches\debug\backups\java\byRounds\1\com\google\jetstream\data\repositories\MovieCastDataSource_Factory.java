package com.google.jetstream.data.repositories;

import com.google.jetstream.data.util.AssetsReader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MovieCastDataSource_Factory implements Factory<MovieCastDataSource> {
  private final Provider<AssetsReader> assetsReaderProvider;

  public MovieCastDataSource_Factory(Provider<AssetsReader> assetsReaderProvider) {
    this.assetsReaderProvider = assetsReaderProvider;
  }

  @Override
  public MovieCastDataSource get() {
    return newInstance(assetsReaderProvider.get());
  }

  public static MovieCastDataSource_Factory create(
      javax.inject.Provider<AssetsReader> assetsReaderProvider) {
    return new MovieCastDataSource_Factory(Providers.asDaggerProvider(assetsReaderProvider));
  }

  public static MovieCastDataSource_Factory create(Provider<AssetsReader> assetsReaderProvider) {
    return new MovieCastDataSource_Factory(assetsReaderProvider);
  }

  public static MovieCastDataSource newInstance(AssetsReader assetsReader) {
    return new MovieCastDataSource(assetsReader);
  }
}
