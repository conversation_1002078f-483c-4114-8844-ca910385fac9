package com.google.jetstream.presentation.screens.profile;

import dagger.internal.DaggerGenerated;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import javax.annotation.processing.Generated;

@IdentifierNameString
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey {
  @KeepFieldType
  static WebDavBrowserViewModel keepFieldType;

  public static String lazyClassKeyName = "com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel";
}
