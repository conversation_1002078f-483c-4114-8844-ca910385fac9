package com.google.jetstream.di;

import android.content.Context;
import com.google.jetstream.data.database.JetStreamDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DatabaseModule_ProvideJetStreamDatabaseFactory implements Factory<JetStreamDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideJetStreamDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public JetStreamDatabase get() {
    return provideJetStreamDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideJetStreamDatabaseFactory create(
      javax.inject.Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideJetStreamDatabaseFactory(Providers.asDaggerProvider(contextProvider));
  }

  public static DatabaseModule_ProvideJetStreamDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideJetStreamDatabaseFactory(contextProvider);
  }

  public static JetStreamDatabase provideJetStreamDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideJetStreamDatabase(context));
  }
}
