package com.google.jetstream.di;

import com.google.jetstream.data.database.JetStreamDatabase;
import com.google.jetstream.data.database.dao.ResourceDirectoryDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DatabaseModule_ProvideResourceDirectoryDaoFactory implements Factory<ResourceDirectoryDao> {
  private final Provider<JetStreamDatabase> databaseProvider;

  public DatabaseModule_ProvideResourceDirectoryDaoFactory(
      Provider<JetStreamDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ResourceDirectoryDao get() {
    return provideResourceDirectoryDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideResourceDirectoryDaoFactory create(
      javax.inject.Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideResourceDirectoryDaoFactory(Providers.asDaggerProvider(databaseProvider));
  }

  public static DatabaseModule_ProvideResourceDirectoryDaoFactory create(
      Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideResourceDirectoryDaoFactory(databaseProvider);
  }

  public static ResourceDirectoryDao provideResourceDirectoryDao(JetStreamDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideResourceDirectoryDao(database));
  }
}
