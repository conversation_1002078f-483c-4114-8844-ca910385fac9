package com.google.jetstream.data.repositories;

import com.google.jetstream.data.database.dao.ScrapedItemDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MovieRepositoryImpl_Factory implements Factory<MovieRepositoryImpl> {
  private final Provider<MovieDataSource> movieDataSourceProvider;

  private final Provider<TvDataSource> tvDataSourceProvider;

  private final Provider<MovieCastDataSource> movieCastDataSourceProvider;

  private final Provider<MovieCategoryDataSource> movieCategoryDataSourceProvider;

  private final Provider<ScrapedItemDao> scrapedItemDaoProvider;

  public MovieRepositoryImpl_Factory(Provider<MovieDataSource> movieDataSourceProvider,
      Provider<TvDataSource> tvDataSourceProvider,
      Provider<MovieCastDataSource> movieCastDataSourceProvider,
      Provider<MovieCategoryDataSource> movieCategoryDataSourceProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    this.movieDataSourceProvider = movieDataSourceProvider;
    this.tvDataSourceProvider = tvDataSourceProvider;
    this.movieCastDataSourceProvider = movieCastDataSourceProvider;
    this.movieCategoryDataSourceProvider = movieCategoryDataSourceProvider;
    this.scrapedItemDaoProvider = scrapedItemDaoProvider;
  }

  @Override
  public MovieRepositoryImpl get() {
    return newInstance(movieDataSourceProvider.get(), tvDataSourceProvider.get(), movieCastDataSourceProvider.get(), movieCategoryDataSourceProvider.get(), scrapedItemDaoProvider.get());
  }

  public static MovieRepositoryImpl_Factory create(
      javax.inject.Provider<MovieDataSource> movieDataSourceProvider,
      javax.inject.Provider<TvDataSource> tvDataSourceProvider,
      javax.inject.Provider<MovieCastDataSource> movieCastDataSourceProvider,
      javax.inject.Provider<MovieCategoryDataSource> movieCategoryDataSourceProvider,
      javax.inject.Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    return new MovieRepositoryImpl_Factory(Providers.asDaggerProvider(movieDataSourceProvider), Providers.asDaggerProvider(tvDataSourceProvider), Providers.asDaggerProvider(movieCastDataSourceProvider), Providers.asDaggerProvider(movieCategoryDataSourceProvider), Providers.asDaggerProvider(scrapedItemDaoProvider));
  }

  public static MovieRepositoryImpl_Factory create(
      Provider<MovieDataSource> movieDataSourceProvider,
      Provider<TvDataSource> tvDataSourceProvider,
      Provider<MovieCastDataSource> movieCastDataSourceProvider,
      Provider<MovieCategoryDataSource> movieCategoryDataSourceProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    return new MovieRepositoryImpl_Factory(movieDataSourceProvider, tvDataSourceProvider, movieCastDataSourceProvider, movieCategoryDataSourceProvider, scrapedItemDaoProvider);
  }

  public static MovieRepositoryImpl newInstance(MovieDataSource movieDataSource,
      TvDataSource tvDataSource, MovieCastDataSource movieCastDataSource,
      MovieCategoryDataSource movieCategoryDataSource, ScrapedItemDao scrapedItemDao) {
    return new MovieRepositoryImpl(movieDataSource, tvDataSource, movieCastDataSource, movieCategoryDataSource, scrapedItemDao);
  }
}
