{"src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerMainFrame.kt": ["VideoPlayerMainFrame:com.google.jetstream.presentation.screens.videoPlayer.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.webdav", "get:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\search\\SearchScreenViewModel_Factory.java": ["newInstance:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_Factory", "get:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_Factory", "SearchScreenViewModel_Factory:com.google.jetstream.presentation.screens.search", "create:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory.java": ["WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.webdav", "INSTANCE:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "provide:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\google\\jetstream\\data\\webdav\\WebDavConfig.kt": ["WebDavConfig:com.google.jetstream.data.webdav", "isEnabled:com.google.jetstream.data.webdav.WebDavConfig", "<init>:com.google.jetstream.data.webdav.WebDavConnectionStatus.CONNECTING", "Loading:com.google.jetstream.data.webdav.WebDavResult", "password:com.google.jetstream.data.webdav.WebDavConfig", "<init>:com.google.jetstream.data.webdav.WebDavConnectionStatus.TESTING", "<init>:com.google.jetstream.data.webdav.WebDavConnectionStatus.IDLE", "data:com.google.jetstream.data.webdav.WebDavResult.Success", "<init>:com.google.jetstream.data.webdav.WebDavResult.Loading", "username:com.google.jetstream.data.webdav.WebDavConfig", "getFormattedServerUrl:com.google.jetstream.data.webdav.WebDavConfig", "message:com.google.jetstream.data.webdav.WebDavResult.Error", "Success:com.google.jetstream.data.webdav.WebDavResult", "<init>:com.google.jetstream.data.webdav.WebDavConnectionStatus.CONNECTED", "<init>:com.google.jetstream.data.webdav.WebDavConnectionStatus.FAILED", "displayName:com.google.jetstream.data.webdav.WebDavConfig", "Error:com.google.jetstream.data.webdav.WebDavResult", "serverUrl:com.google.jetstream.data.webdav.WebDavConfig", "IDLE:com.google.jetstream.data.webdav.WebDavConnectionStatus", "WebDavResult:com.google.jetstream.data.webdav", "FAILED:com.google.jetstream.data.webdav.WebDavConnectionStatus", "exception:com.google.jetstream.data.webdav.WebDavResult.Error", "CONNECTING:com.google.jetstream.data.webdav.WebDavConnectionStatus", "CONNECTED:com.google.jetstream.data.webdav.WebDavConnectionStatus", "WebDavConnectionStatus:com.google.jetstream.data.webdav", "<init>:com.google.jetstream.data.webdav.WebDavResult", "isValid:com.google.jetstream.data.webdav.WebDavConfig", "TESTING:com.google.jetstream.data.webdav.WebDavConnectionStatus"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardScreen.kt": ["DashboardScreen:com.google.jetstream.presentation.screens.dashboard", "ParentPadding:com.google.jetstream.presentation.screens.dashboard", "rememberChildPadding:com.google.jetstream.presentation.screens.dashboard"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["create:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.movies", "<init>:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\favourites\\MovieFilterChip.kt": ["MovieFilterChip:com.google.jetstream.presentation.screens.favourites"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "MoviesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.movies"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\dao\\ResourceDirectoryDao.kt": ["getDirectoryById:com.google.jetstream.data.database.dao.ResourceDirectoryDao", "deleteDirectory:com.google.jetstream.data.database.dao.ResourceDirectoryDao", "ResourceDirectoryDao:com.google.jetstream.data.database.dao", "getAllDirectories:com.google.jetstream.data.database.dao.ResourceDirectoryDao", "getDirectoriesByConfigId:com.google.jetstream.data.database.dao.ResourceDirectoryDao", "insertDirectory:com.google.jetstream.data.database.dao.ResourceDirectoryDao", "updateDirectory:com.google.jetstream.data.database.dao.ResourceDirectoryDao", "deleteDirectoryById:com.google.jetstream.data.database.dao.ResourceDirectoryDao", "deleteDirectoriesByConfigId:com.google.jetstream.data.database.dao.ResourceDirectoryDao"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoryMovieListScreenViewModel_Factory.java": ["<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_Factory", "CategoryMovieListScreenViewModel_Factory:com.google.jetstream.presentation.screens.categories", "get:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_Factory", "create:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedMoviesViewModel.kt": ["movies:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel", "ScrapedMoviesViewModel:com.google.jetstream.presentation.screens.home"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_HomeScreeViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_home_HomeScreeViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_HomeScreeViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\theme\\JetStreamFocusTheme.kt": ["JetStreamCardShape:com.google.jetstream.presentation.theme", "JetStreamBorderWidth:com.google.jetstream.presentation.theme", "JetStreamButtonShape:com.google.jetstream.presentation.theme", "JetStreamBottomListPadding:com.google.jetstream.presentation.theme", "IconSize:com.google.jetstream.presentation.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_google_jetstream_di_DatabaseModule.java": ["_com_google_jetstream_di_DatabaseModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_di_DatabaseModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavListDialog.kt": ["displayName:com.google.jetstream.presentation.screens.profile.WebDavConfig", "username:com.google.jetstream.presentation.screens.profile.WebDavConfig", "isConnected:com.google.jetstream.presentation.screens.profile.WebDavConfig", "serverUrl:com.google.jetstream.presentation.screens.profile.WebDavConfig", "WebDavListDialog:com.google.jetstream.presentation.screens.profile", "WebDavConfig:com.google.jetstream.presentation.screens.profile", "id:com.google.jetstream.presentation.screens.profile.WebDavConfig"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoriesScreen.kt": ["CategoriesScreen:com.google.jetstream.presentation.screens.categories"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_categories_CategoryMovieListScreenViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_categories_CategoryMovieListScreenViewModel_HiltModules_BindsModule", "_com_google_jetstream_presentation_screens_categories_CategoryMovieListScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\DotSeparatedRow.kt": ["DotSeparatedRow:com.google.jetstream.presentation.screens.movies"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavBrowserViewModel_Factory.java": ["<init>:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_Factory", "get:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_Factory", "WebDavBrowserViewModel_Factory:com.google.jetstream.presentation.screens.profile", "create:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory.java": ["get:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory", "RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.home", "INSTANCE:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\Hilt_MainActivity.java": ["Hilt_MainActivity:com.google.jetstream", "createComponentManager:com.google.jetstream.Hilt_MainActivity", "onCreate:com.google.jetstream.Hilt_MainActivity", "componentManager:com.google.jetstream.Hilt_MainActivity", "generatedComponent:com.google.jetstream.Hilt_MainActivity", "inject:com.google.jetstream.Hilt_MainActivity", "<init>:com.google.jetstream.Hilt_MainActivity", "onDestroy:com.google.jetstream.Hilt_MainActivity", "getDefaultViewModelProviderFactory:com.google.jetstream.Hilt_MainActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\HomeScreeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "HomeScreeViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.home", "lazyClassKeyName:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\home\\Top10MoviesList.kt": ["Top10MoviesList:com.google.jetstream.presentation.screens.home"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardTopBar.kt": ["TopBarFocusRequesters:com.google.jetstream.presentation.screens.dashboard", "TopBarTabs:com.google.jetstream.presentation.screens.dashboard", "DashboardTopBar:com.google.jetstream.presentation.screens.dashboard"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\home\\HomeScreen.kt": ["HomeScreen:com.google.jetstream.presentation.screens.home"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetails.kt": ["second:com.google.jetstream.presentation.screens.movies.Quad", "SourceInfoAndSpecs:com.google.jetstream.presentation.screens.movies", "first:com.google.jetstream.presentation.screens.movies.Quad", "fourth:com.google.jetstream.presentation.screens.movies.Quad", "third:com.google.jetstream.presentation.screens.movies.Quad", "MovieDetails:com.google.jetstream.presentation.screens.movies"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\ScrapedMoviesStore.kt": ["clear:com.google.jetstream.data.repositories.ScrapedMoviesStore", "movies:com.google.jetstream.data.repositories.ScrapedMoviesStore", "setMovies:com.google.jetstream.data.repositories.ScrapedMoviesStore", "ScrapedMoviesStore:com.google.jetstream.data.repositories"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetailsScreen.kt": ["MovieDetailsScreen:com.google.jetstream.presentation.screens.movies", "<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreen", "MovieIdBundleKey:com.google.jetstream.presentation.screens.movies.MovieDetailsScreen"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movietype\\MovieTypeListScreen.kt": ["<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState.Loading", "<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState.Error", "MovieTypeKey:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreen", "Ready:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState", "Loading:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState", "MovieTypeListScreenUiState:com.google.jetstream.presentation.screens.movietype", "title:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState.Ready", "<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreen", "MovieTypeListScreen:com.google.jetstream.presentation.screens.movietype", "movies:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState.Ready", "Error:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedTvViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["ScrapedTvViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.home", "keepFieldType:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\home\\RecentlyWatchedViewModel.kt": ["RecentlyWatchedViewModel:com.google.jetstream.presentation.screens.home", "recentlyWatchedMovies:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\RecentlyWatchedRepository.kt": ["getDao:com.google.jetstream.data.repositories.RecentlyWatchedRepository", "removeRecentlyWatched:com.google.jetstream.data.repositories.RecentlyWatchedRepository", "RecentlyWatchedRepository:com.google.jetstream.data.repositories", "getRecentlyWatchedMovies:com.google.jetstream.data.repositories.RecentlyWatchedRepository", "getRecentlyWatchedByMovieId:com.google.jetstream.data.repositories.RecentlyWatchedRepository", "addRecentlyWatched:com.google.jetstream.data.repositories.RecentlyWatchedRepository", "clearAllRecentlyWatched:com.google.jetstream.data.repositories.RecentlyWatchedRepository"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\DirectoryPickerDialog.kt": ["DirectoryPickerDialog:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movietype\\MovieTypeListScreenViewModel.kt": ["uiState:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel", "MovieTypeListScreenViewModel:com.google.jetstream.presentation.screens.movietype"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedMoviesViewModel_Factory.java": ["<init>:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_Factory", "ScrapedMoviesViewModel_Factory:com.google.jetstream.presentation.screens.home", "get:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_Factory", "create:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedTvViewModel_HiltModules.java": ["BindsModule:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules", "provide:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules.KeyModule", "binds:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules.BindsModule", "ScrapedTvViewModel_HiltModules:com.google.jetstream.presentation.screens.home", "KeyModule:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavBrowserViewModel_Factory.java": ["get:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_Factory", "WebDavBrowserViewModel_Factory:com.google.jetstream.presentation.screens.webdav", "newInstance:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_Factory", "create:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\HelpAndSupportSection.kt": ["HelpAndSupportSection:com.google.jetstream.presentation.screens.profile"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_videoPlayer_VideoPlayerScreenViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_videoPlayer_VideoPlayerScreenViewModel_HiltModules_BindsModule", "_com_google_jetstream_presentation_screens_videoPlayer_VideoPlayerScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movietype\\MovieTypeListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "MovieTypeListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.movietype", "keepFieldType:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_search_SearchScreenViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_search_SearchScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_search_SearchScreenViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardViewModel_Factory.java": ["newInstance:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_Factory", "get:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_Factory", "DashboardViewModel_Factory:com.google.jetstream.presentation.screens.dashboard", "<init>:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_Factory", "create:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\ScrapedTvStore_Factory.java": ["create:com.google.jetstream.data.repositories.ScrapedTvStore_Factory", "ScrapedTvStore_Factory:com.google.jetstream.data.repositories", "newInstance:com.google.jetstream.data.repositories.ScrapedTvStore_Factory", "INSTANCE:com.google.jetstream.data.repositories.ScrapedTvStore_Factory.InstanceHolder", "<init>:com.google.jetstream.data.repositories.ScrapedTvStore_Factory.InstanceHolder", "<init>:com.google.jetstream.data.repositories.ScrapedTvStore_Factory", "get:com.google.jetstream.data.repositories.ScrapedTvStore_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\VideoPlayerScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "VideoPlayerScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.videoPlayer", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_RecentlyWatchedViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_home_RecentlyWatchedViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_RecentlyWatchedViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\shows\\ShowScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "ShowScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.shows", "lazyClassKeyName:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavConfigScreen.kt": ["WebDavConfigScreen:com.google.jetstream.presentation.screens.webdav"], "src\\main\\java\\com\\google\\jetstream\\JetStreamApplication.kt": ["JetStreamApplication:com.google.jetstream", "bindMovieRepository:com.google.jetstream.MovieRepositoryModule", "MovieRepositoryModule:com.google.jetstream", "<init>:com.google.jetstream.JetStreamApplication", "<init>:com.google.jetstream.MovieRepositoryModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\RecentlyWatchedViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["RecentlyWatchedViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.home", "<init>:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardViewModel.kt": ["<init>:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel.Companion", "DashboardViewModel:com.google.jetstream.presentation.screens.dashboard", "isRefreshing:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel", "refreshAndScrape:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel", "page:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchResponse", "firstAirDate:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "name:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "releaseDate:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "SearchItem:com.google.jetstream.presentation.screens.dashboard.TmdbApi", "title:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "voteAverage:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "SearchResponse:com.google.jetstream.presentation.screens.dashboard.TmdbApi", "originalName:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "<init>:com.google.jetstream.presentation.screens.dashboard.TmdbApi", "Companion:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel", "results:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchResponse", "overview:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "search:com.google.jetstream.presentation.screens.dashboard.TmdbApi", "id:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "posterPath:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem", "originalTitle:com.google.jetstream.presentation.screens.dashboard.TmdbApi.SearchItem"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\favourites\\FavouritesScreen.kt": ["FavouritesScreen:com.google.jetstream.presentation.screens.favourites"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_search_SearchScreenViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_search_SearchScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_search_SearchScreenViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\SubtitlesSection.kt": ["SubtitlesSection:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\data\\services\\EpisodeMatchingService.kt": ["Companion:com.google.jetstream.data.services.EpisodeMatchingService", "<init>:com.google.jetstream.data.services.EpisodeMatchingService.Companion", "getFilteredEpisodes:com.google.jetstream.data.services.EpisodeMatchingService", "EpisodeMatchingService:com.google.jetstream.data.services"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_movies_MovieDetailsScreenViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_movies_MovieDetailsScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_movies_MovieDetailsScreenViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\search\\SearchScreen.kt": ["SearchResult:com.google.jetstream.presentation.screens.search", "SearchScreen:com.google.jetstream.presentation.screens.search"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavConfigViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "WebDavConfigViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.webdav"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\dashboard\\UserAvatar.kt": ["UserAvatar:com.google.jetstream.presentation.screens.dashboard"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\TitleValueText.kt": ["TitleValueText:com.google.jetstream.presentation.screens.movies"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreenViewModel.kt": ["MoviesScreenUiState:com.google.jetstream.presentation.screens.movies", "MoviesScreenViewModel:com.google.jetstream.presentation.screens.movies", "<init>:com.google.jetstream.presentation.screens.movies.MoviesScreenUiState.Loading", "uiState:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel", "Loading:com.google.jetstream.presentation.screens.movies.MoviesScreenUiState", "popularFilmsThisWeek:com.google.jetstream.presentation.screens.movies.MoviesScreenUiState.Ready", "Ready:com.google.jetstream.presentation.screens.movies.MoviesScreenUiState"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\database\\dao\\ResourceDirectoryDao_Impl.java": ["getDirectoryById:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "deleteDirectoriesByConfigId:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "deleteDirectoryById:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "deleteDirectory:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "updateDirectory:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "insertDirectory:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "getAllDirectories:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "getDirectoriesByConfigId:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "ResourceDirectoryDao_Impl:com.google.jetstream.data.database.dao", "<init>:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl", "getRequiredConverters:com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\dao\\ScrapedItemDao.kt": ["getAllByType:com.google.jetstream.data.database.dao.ScrapedItemDao", "clearAll:com.google.jetstream.data.database.dao.ScrapedItemDao", "upsertAll:com.google.jetstream.data.database.dao.ScrapedItemDao", "ScrapedItemDao:com.google.jetstream.data.database.dao", "getById:com.google.jetstream.data.database.dao.ScrapedItemDao"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetailsScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "MovieDetailsScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.movies", "lazyClassKeyName:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["provide:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory", "CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.categories", "get:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\RecentlyWatchedRepository_Factory.java": ["RecentlyWatchedRepository_Factory:com.google.jetstream.data.repositories", "get:com.google.jetstream.data.repositories.RecentlyWatchedRepository_Factory", "<init>:com.google.jetstream.data.repositories.RecentlyWatchedRepository_Factory", "create:com.google.jetstream.data.repositories.RecentlyWatchedRepository_Factory", "newInstance:com.google.jetstream.data.repositories.RecentlyWatchedRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\search\\SearchScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "SearchScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.search", "keepFieldType:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.profile", "get:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoriesScreenViewModel_HiltModules.java": ["BindsModule:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules", "CategoriesScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.categories", "binds:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\utils\\ModifierUtils.kt": ["handleDPadKeyEvents:com.google.jetstream.presentation.utils", "parentModifier:com.google.jetstream.presentation.utils.FocusRequesterModifiers", "ifElse:com.google.jetstream.presentation.utils", "FocusRequesterModifiers:com.google.jetstream.presentation.utils", "focusOnInitialVisibility:com.google.jetstream.presentation.utils", "childModifier:com.google.jetstream.presentation.utils.FocusRequesterModifiers", "occupyScreenSize:com.google.jetstream.presentation.utils", "createInitialFocusRestorerModifiers:com.google.jetstream.presentation.utils"], "src\\main\\java\\com\\google\\jetstream\\presentation\\utils\\BringIntoViewIfChildrenAreFocused.kt": ["bringIntoViewIfChildrenAreFocused:com.google.jetstream.presentation.utils"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\database\\dao\\WebDavConfigDao_Impl.java": ["updateConfig:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl", "<init>:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl", "getConfigById:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl", "WebDavConfigDao_Impl:com.google.jetstream.data.database.dao", "deleteConfig:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl", "deleteConfigById:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl", "getAllConfigs:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl", "insertConfig:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl", "getRequiredConverters:com.google.jetstream.data.database.dao.WebDavConfigDao_Impl"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\shows\\ShowScreenViewModel_Factory.java": ["ShowScreenViewModel_Factory:com.google.jetstream.presentation.screens.shows", "get:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_Factory", "create:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\favourites\\FavouriteScreenViewModel_Factory.java": ["create:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_Factory", "FavouriteScreenViewModel_Factory:com.google.jetstream.presentation.screens.favourites", "get:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\entities\\RecentlyWatchedEntity.kt": ["episodeId:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "currentPositionMs:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "episodeTitle:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "durationMs:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "lastWatchedAt:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "rating:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "type:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "watchProgress:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "description:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "releaseDate:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "movieId:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "movieTitle:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "RecentlyWatchedEntity:com.google.jetstream.data.database.entities", "backdropUri:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "seasonNumber:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "episodeNumber:com.google.jetstream.data.database.entities.RecentlyWatchedEntity", "posterUri:com.google.jetstream.data.database.entities.RecentlyWatchedEntity"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\dao\\RecentlyWatchedDao.kt": ["deleteByMovieId:com.google.jetstream.data.database.dao.RecentlyWatchedDao", "clearAll:com.google.jetstream.data.database.dao.RecentlyWatchedDao", "RecentlyWatchedDao:com.google.jetstream.data.database.dao", "insertOrUpdate:com.google.jetstream.data.database.dao.RecentlyWatchedDao", "getRecentlyWatched:com.google.jetstream.data.database.dao.RecentlyWatchedDao", "getByMovieId:com.google.jetstream.data.database.dao.RecentlyWatchedDao", "getCount:com.google.jetstream.data.database.dao.RecentlyWatchedDao", "deleteOldest:com.google.jetstream.data.database.dao.RecentlyWatchedDao"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerOverlay.kt": ["VideoPlayerOverlay:com.google.jetstream.presentation.screens.videoPlayer.components", "CinematicBackground:com.google.jetstream.presentation.screens.videoPlayer.components"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\AccountsSelectionItem.kt": ["AccountsSelectionItem:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\MovieDetails.kt": ["releaseDate:com.google.jetstream.data.entities.MovieDetails", "webDavPath:com.google.jetstream.data.entities.TvSeason", "id:com.google.jetstream.data.entities.MovieDetails", "categories:com.google.jetstream.data.entities.MovieDetails", "castAndCrew:com.google.jetstream.data.entities.MovieDetails", "name:com.google.jetstream.data.entities.TvSeason", "description:com.google.jetstream.data.entities.MovieDetails", "videoUri:com.google.jetstream.data.entities.MovieDetails", "music:com.google.jetstream.data.entities.MovieDetails", "number:com.google.jetstream.data.entities.TvSeason", "episodeCount:com.google.jetstream.data.entities.TvSeason", "screenplay:com.google.jetstream.data.entities.MovieDetails", "director:com.google.jetstream.data.entities.MovieDetails", "backdropUri:com.google.jetstream.data.entities.MovieDetails", "MovieDetails:com.google.jetstream.data.entities", "posterUri:com.google.jetstream.data.entities.MovieDetails", "name:com.google.jetstream.data.entities.MovieDetails", "pgRating:com.google.jetstream.data.entities.MovieDetails", "availableSeasons:com.google.jetstream.data.entities.MovieDetails", "subtitleUri:com.google.jetstream.data.entities.MovieDetails", "duration:com.google.jetstream.data.entities.MovieDetails", "isTV:com.google.jetstream.data.entities.MovieDetails", "TvSeason:com.google.jetstream.data.entities"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory.java": ["HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.home", "get:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "INSTANCE:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\shows\\ShowScreenViewModel_HiltModules.java": ["ShowScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.shows", "provide:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules.KeyModule", "BindsModule:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules", "binds:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules.BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_categories_CategoryMovieListScreenViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_categories_CategoryMovieListScreenViewModel_HiltModules_KeyModule", "_com_google_jetstream_presentation_screens_categories_CategoryMovieListScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\WebDavRepository_Factory.java": ["<init>:com.google.jetstream.data.repositories.WebDavRepository_Factory", "newInstance:com.google.jetstream.data.repositories.WebDavRepository_Factory", "get:com.google.jetstream.data.repositories.WebDavRepository_Factory", "WebDavRepository_Factory:com.google.jetstream.data.repositories", "create:com.google.jetstream.data.repositories.WebDavRepository_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerControllerText.kt": ["VideoPlayerControllerText:com.google.jetstream.presentation.screens.videoPlayer.components"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\MovieCategory.kt": ["id:com.google.jetstream.data.entities.MovieCategory", "toMovieCategory:com.google.jetstream.data.entities", "MovieCategory:com.google.jetstream.data.entities", "name:com.google.jetstream.data.entities.MovieCategory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetailsScreenViewModel_HiltModules.java": ["KeyModule:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules", "BindsModule:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules", "provide:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules.KeyModule", "binds:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules.BindsModule", "MovieDetailsScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.movies"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedMoviesViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "ScrapedMoviesViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.home", "keepFieldType:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\entities\\ResourceDirectoryEntity.kt": ["serverName:com.google.jetstream.data.database.entities.ResourceDirectoryEntity", "createdAt:com.google.jetstream.data.database.entities.ResourceDirectoryEntity", "id:com.google.jetstream.data.database.entities.ResourceDirectoryEntity", "webDavConfigId:com.google.jetstream.data.database.entities.ResourceDirectoryEntity", "path:com.google.jetstream.data.database.entities.ResourceDirectoryEntity", "name:com.google.jetstream.data.database.entities.ResourceDirectoryEntity", "ResourceDirectoryEntity:com.google.jetstream.data.database.entities"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "provide:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory", "MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.movies", "INSTANCE:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\AccountsSection.kt": ["title:com.google.jetstream.presentation.screens.profile.AccountsSectionData", "AccountsSectionData:com.google.jetstream.presentation.screens.profile", "value:com.google.jetstream.presentation.screens.profile.AccountsSectionData", "AccountsSection:com.google.jetstream.presentation.screens.profile", "onClick:com.google.jetstream.presentation.screens.profile.AccountsSectionData"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreenMovieList.kt": ["MoviesScreenMovieList:com.google.jetstream.presentation.screens.movies"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\MovieCategoryList.kt": ["MovieCategoryList:com.google.jetstream.data.entities"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\JetStreamApplication_GeneratedInjector.java": ["JetStreamApplication_GeneratedInjector:com.google.jetstream", "injectJetStreamApplication:com.google.jetstream.JetStreamApplication_GeneratedInjector"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\home\\FeaturedMoviesCarousel.kt": ["FeaturedMoviesCarousel:com.google.jetstream.presentation.screens.home", "CarouselSaver:com.google.jetstream.presentation.screens.home"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerControls.kt": ["VideoPlayerControls:com.google.jetstream.presentation.screens.videoPlayer.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_ScrapedTvViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_home_ScrapedTvViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_ScrapedTvViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\search\\SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["provide:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory", "SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.search", "INSTANCE:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_ScrapedTvViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_home_ScrapedTvViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_ScrapedTvViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.videoPlayer", "provide:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\Screens.kt": ["isTabItem:com.google.jetstream.presentation.screens.Screens", "<init>:com.google.jetstream.presentation.screens.Screens.Categories", "MovieDetails:com.google.jetstream.presentation.screens.Screens", "<init>:com.google.jetstream.presentation.screens.Screens.Dashboard", "Profile:com.google.jetstream.presentation.screens.Screens", "tabIcon:com.google.jetstream.presentation.screens.Screens", "<init>:com.google.jetstream.presentation.screens.Screens.Profile", "VideoPlayer:com.google.jetstream.presentation.screens.Screens", "<init>:com.google.jetstream.presentation.screens.Screens.MovieDetails", "<init>:com.google.jetstream.presentation.screens.Screens.VideoPlayer", "Favourites:com.google.jetstream.presentation.screens.Screens", "CategoryMovieList:com.google.jetstream.presentation.screens.Screens", "<init>:com.google.jetstream.presentation.screens.Screens.CategoryMovieList", "<init>:com.google.jetstream.presentation.screens.Screens.Home", "<init>:com.google.jetstream.presentation.screens.Screens.Shows", "Search:com.google.jetstream.presentation.screens.Screens", "Screens:com.google.jetstream.presentation.screens", "Dashboard:com.google.jetstream.presentation.screens.Screens", "Categories:com.google.jetstream.presentation.screens.Screens", "Home:com.google.jetstream.presentation.screens.Screens", "<init>:com.google.jetstream.presentation.screens.Screens.Favourites", "<init>:com.google.jetstream.presentation.screens.Screens.Search", "<init>:com.google.jetstream.presentation.screens.Screens.Movies", "Movies:com.google.jetstream.presentation.screens.Screens", "<init>:com.google.jetstream.presentation.screens.Screens.MovieTypeList", "Shows:com.google.jetstream.presentation.screens.Screens", "invoke:com.google.jetstream.presentation.screens.Screens", "withArgs:com.google.jetstream.presentation.screens.Screens", "MovieTypeList:com.google.jetstream.presentation.screens.Screens"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_webdav_WebDavBrowserViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_webdav_WebDavBrowserViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_webdav_WebDavBrowserViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoryMovieListScreen.kt": ["CategoryMovieListScreen:com.google.jetstream.presentation.screens.categories", "<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreen", "CategoryIdBundleKey:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreen"], "src\\main\\java\\com\\google\\jetstream\\tvmaterial\\Shapes.kt": ["top:com.google.jetstream.tvmaterial", "end:com.google.jetstream.tvmaterial"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_profile_WebDavBrowserViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_profile_WebDavBrowserViewModel_HiltModules_BindsModule", "_com_google_jetstream_presentation_screens_profile_WebDavBrowserViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_categories_CategoriesScreenViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_categories_CategoriesScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_categories_CategoriesScreenViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_MainActivity_GeneratedInjector.java": ["_com_google_jetstream_MainActivity_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_MainActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoryMovieListScreenViewModel_HiltModules.java": ["provide:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules.KeyModule", "binds:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules.BindsModule", "KeyModule:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules", "CategoryMovieListScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.categories", "BindsModule:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\RecentlyWatchedViewModel_Factory.java": ["get:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_Factory", "create:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_Factory", "RecentlyWatchedViewModel_Factory:com.google.jetstream.presentation.screens.home"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.categories", "get:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\WebDavRepository.kt": ["getWebDavConfigById:com.google.jetstream.data.repositories.WebDavRepository", "WebDavRepository:com.google.jetstream.data.repositories", "deleteResourceDirectory:com.google.jetstream.data.repositories.WebDavRepository", "saveWebDavConfig:com.google.jetstream.data.repositories.WebDavRepository", "getAllWebDavConfigs:com.google.jetstream.data.repositories.WebDavRepository", "getWebDavConfigEntityById:com.google.jetstream.data.repositories.WebDavRepository", "deleteWebDavConfig:com.google.jetstream.data.repositories.WebDavRepository", "getAllResourceDirectories:com.google.jetstream.data.repositories.WebDavRepository", "saveResourceDirectory:com.google.jetstream.data.repositories.WebDavRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_JetStreamApplication_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_JetStreamApplication_GeneratedInjector", "_com_google_jetstream_JetStreamApplication_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_movietype_MovieTypeListScreenViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_movietype_MovieTypeListScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_movietype_MovieTypeListScreenViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_categories_CategoriesScreenViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_categories_CategoriesScreenViewModel_HiltModules_KeyModule", "_com_google_jetstream_presentation_screens_categories_CategoriesScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\JetStreamDatabase.kt": ["Companion:com.google.jetstream.data.database.JetStreamDatabase", "scrapedItemDao:com.google.jetstream.data.database.JetStreamDatabase", "recentlyWatchedDao:com.google.jetstream.data.database.JetStreamDatabase", "JetStreamDatabase:com.google.jetstream.data.database", "resourceDirectoryDao:com.google.jetstream.data.database.JetStreamDatabase", "getDatabase:com.google.jetstream.data.database.JetStreamDatabase.Companion", "webDavConfigDao:com.google.jetstream.data.database.JetStreamDatabase", "<init>:com.google.jetstream.data.database.JetStreamDatabase.Companion", "<init>:com.google.jetstream.data.database.JetStreamDatabase"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\di\\DatabaseModule_ProvideScrapedItemDaoFactory.java": ["provideScrapedItemDao:com.google.jetstream.di.DatabaseModule_ProvideScrapedItemDaoFactory", "get:com.google.jetstream.di.DatabaseModule_ProvideScrapedItemDaoFactory", "DatabaseModule_ProvideScrapedItemDaoFactory:com.google.jetstream.di", "create:com.google.jetstream.di.DatabaseModule_ProvideScrapedItemDaoFactory", "<init>:com.google.jetstream.di.DatabaseModule_ProvideScrapedItemDaoFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\favourites\\FavouriteScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "FavouriteScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.favourites"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\di\\DatabaseModule_ProvideJetStreamDatabaseFactory.java": ["create:com.google.jetstream.di.DatabaseModule_ProvideJetStreamDatabaseFactory", "provideJetStreamDatabase:com.google.jetstream.di.DatabaseModule_ProvideJetStreamDatabaseFactory", "get:com.google.jetstream.di.DatabaseModule_ProvideJetStreamDatabaseFactory", "<init>:com.google.jetstream.di.DatabaseModule_ProvideJetStreamDatabaseFactory", "DatabaseModule_ProvideJetStreamDatabaseFactory:com.google.jetstream.di"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_movietype_MovieTypeListScreenViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_movietype_MovieTypeListScreenViewModel_HiltModules_KeyModule", "_com_google_jetstream_presentation_screens_movietype_MovieTypeListScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoriesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["CategoriesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.categories", "keepFieldType:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\RecentlyWatchedViewModel_HiltModules.java": ["provide:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules.KeyModule", "RecentlyWatchedViewModel_HiltModules:com.google.jetstream.presentation.screens.home", "BindsModule:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules", "binds:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules.BindsModule", "KeyModule:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\MovieRepositoryImpl_Factory.java": ["<init>:com.google.jetstream.data.repositories.MovieRepositoryImpl_Factory", "get:com.google.jetstream.data.repositories.MovieRepositoryImpl_Factory", "newInstance:com.google.jetstream.data.repositories.MovieRepositoryImpl_Factory", "create:com.google.jetstream.data.repositories.MovieRepositoryImpl_Factory", "MovieRepositoryImpl_Factory:com.google.jetstream.data.repositories"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_google_jetstream_MovieRepositoryModule.java": ["_com_google_jetstream_MovieRepositoryModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_MovieRepositoryModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetailsScreenViewModel_Factory.java": ["create:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_Factory", "get:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_Factory", "MovieDetailsScreenViewModel_Factory:com.google.jetstream.presentation.screens.movies", "<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\VideoPlayerScreen.kt": ["EpisodeIdBundleKey:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreen", "VideoPlayerScreenContent:com.google.jetstream.presentation.screens.videoPlayer", "VideoPlayerScreen:com.google.jetstream.presentation.screens.videoPlayer", "MovieIdBundleKey:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreen", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreen"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\ScrapedTvStore.kt": ["setShows:com.google.jetstream.data.repositories.ScrapedTvStore", "clear:com.google.jetstream.data.repositories.ScrapedTvStore", "shows:com.google.jetstream.data.repositories.ScrapedTvStore", "ScrapedTvStore:com.google.jetstream.data.repositories"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\di\\DatabaseModule_ProvideWebDavConfigDaoFactory.java": ["DatabaseModule_ProvideWebDavConfigDaoFactory:com.google.jetstream.di", "provideWebDavConfigDao:com.google.jetstream.di.DatabaseModule_ProvideWebDavConfigDaoFactory", "get:com.google.jetstream.di.DatabaseModule_ProvideWebDavConfigDaoFactory", "<init>:com.google.jetstream.di.DatabaseModule_ProvideWebDavConfigDaoFactory", "create:com.google.jetstream.di.DatabaseModule_ProvideWebDavConfigDaoFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoriesScreenViewModel_Factory.java": ["CategoriesScreenViewModel_Factory:com.google.jetstream.presentation.screens.categories", "create:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_Factory", "get:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\HomeScreeViewModel_HiltModules.java": ["binds:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules.KeyModule", "HomeScreeViewModel_HiltModules:com.google.jetstream.presentation.screens.home", "BindsModule:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_webdav_WebDavConfigViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_webdav_WebDavConfigViewModel_HiltModules_BindsModule", "_com_google_jetstream_presentation_screens_webdav_WebDavConfigViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerIndicator.kt": ["VideoPlayerControllerIndicator:com.google.jetstream.presentation.screens.videoPlayer.components"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\AboutSection.kt": ["AboutSection:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\presentation\\common\\PosterImage.kt": ["PosterImage:com.google.jetstream.presentation.common"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\MovieCastDataSource_Factory.java": ["get:com.google.jetstream.data.repositories.MovieCastDataSource_Factory", "newInstance:com.google.jetstream.data.repositories.MovieCastDataSource_Factory", "<init>:com.google.jetstream.data.repositories.MovieCastDataSource_Factory", "MovieCastDataSource_Factory:com.google.jetstream.data.repositories", "create:com.google.jetstream.data.repositories.MovieCastDataSource_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\App.kt": ["App:com.google.jetstream.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\data\\webdav\\WebDavService.kt": ["testConnection:com.google.jetstream.data.webdav.WebDavService", "isConfigured:com.google.jetstream.data.webdav.WebDavService", "clearConfig:com.google.jetstream.data.webdav.WebDavService", "setConfig:com.google.jetstream.data.webdav.WebDavService", "WebDavService:com.google.jetstream.data.webdav", "getCurrentConfig:com.google.jetstream.data.webdav.WebDavService", "listDirectory:com.google.jetstream.data.webdav.WebDavService", "statFileSizeByUrl:com.google.jetstream.data.webdav.WebDavService"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.webdav", "lazyClassKeyName:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\HomeScreeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "HomeScreeViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.home", "lazyClassKeyName:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\services\\EpisodeMatchingService_Factory.java": ["create:com.google.jetstream.data.services.EpisodeMatchingService_Factory", "get:com.google.jetstream.data.services.EpisodeMatchingService_Factory", "<init>:com.google.jetstream.data.services.EpisodeMatchingService_Factory", "EpisodeMatchingService_Factory:com.google.jetstream.data.services", "newInstance:com.google.jetstream.data.services.EpisodeMatchingService_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\ProfileScreens.kt": ["Language:com.google.jetstream.presentation.screens.profile.ProfileScreens", "<init>:com.google.jetstream.presentation.screens.profile.ProfileScreens.SearchHistory", "ProfileScreens:com.google.jetstream.presentation.screens.profile", "About:com.google.jetstream.presentation.screens.profile.ProfileScreens", "HelpAndSupport:com.google.jetstream.presentation.screens.profile.ProfileScreens", "<init>:com.google.jetstream.presentation.screens.profile.ProfileScreens.WebDavBrowser", "SearchHistory:com.google.jetstream.presentation.screens.profile.ProfileScreens", "<init>:com.google.jetstream.presentation.screens.profile.ProfileScreens.HelpAndSupport", "<init>:com.google.jetstream.presentation.screens.profile.ProfileScreens.Accounts", "<init>:com.google.jetstream.presentation.screens.profile.ProfileScreens.Subtitles", "WebDavBrowser:com.google.jetstream.presentation.screens.profile.ProfileScreens", "<init>:com.google.jetstream.presentation.screens.profile.ProfileScreens.About", "icon:com.google.jetstream.presentation.screens.profile.ProfileScreens", "invoke:com.google.jetstream.presentation.screens.profile.ProfileScreens", "<init>:com.google.jetstream.presentation.screens.profile.ProfileScreens.Language", "tabTitle:com.google.jetstream.presentation.screens.profile.ProfileScreens", "Accounts:com.google.jetstream.presentation.screens.profile.ProfileScreens", "Subtitles:com.google.jetstream.presentation.screens.profile.ProfileScreens"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_webdav_WebDavBrowserViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_webdav_WebDavBrowserViewModel_HiltModules_KeyModule", "_com_google_jetstream_presentation_screens_webdav_WebDavBrowserViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_favourites_FavouriteScreenViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_favourites_FavouriteScreenViewModel_HiltModules_BindsModule", "_com_google_jetstream_presentation_screens_favourites_FavouriteScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "src\\main\\java\\com\\google\\jetstream\\presentation\\common\\Loading.kt": ["Loading:com.google.jetstream.presentation.common"], "build\\generated\\source\\buildConfig\\debug\\com\\google\\jetstream\\BuildConfig.java": ["BuildConfig:com.google.jetstream", "APPLICATION_ID:com.google.jetstream.BuildConfig", "BUILD_TYPE:com.google.jetstream.BuildConfig", "<init>:com.google.jetstream.BuildConfig", "VERSION_CODE:com.google.jetstream.BuildConfig", "VERSION_NAME:com.google.jetstream.BuildConfig", "DEBUG:com.google.jetstream.BuildConfig"], "src\\main\\java\\com\\google\\jetstream\\presentation\\common\\MoviesRow.kt": ["<init>:com.google.jetstream.presentation.common.ItemDirection.Vertical", "<init>:com.google.jetstream.presentation.common.ItemDirection.Horizontal", "ItemDirection:com.google.jetstream.presentation.common", "MoviesRow:com.google.jetstream.presentation.common", "Horizontal:com.google.jetstream.presentation.common.ItemDirection", "ImmersiveListMoviesRow:com.google.jetstream.presentation.common", "Vertical:com.google.jetstream.presentation.common.ItemDirection", "aspectRatio:com.google.jetstream.presentation.common.ItemDirection"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\MovieList.kt": ["MovieList:com.google.jetstream.data.entities"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\MovieCategoryDataSource_Factory.java": ["get:com.google.jetstream.data.repositories.MovieCategoryDataSource_Factory", "newInstance:com.google.jetstream.data.repositories.MovieCategoryDataSource_Factory", "create:com.google.jetstream.data.repositories.MovieCategoryDataSource_Factory", "MovieCategoryDataSource_Factory:com.google.jetstream.data.repositories", "<init>:com.google.jetstream.data.repositories.MovieCategoryDataSource_Factory"], "src\\main\\java\\com\\google\\jetstream\\MainActivity.kt": ["onCreate:com.google.jetstream.MainActivity", "MainActivity:com.google.jetstream", "<init>:com.google.jetstream.MainActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_shows_ShowScreenViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_shows_ShowScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_shows_ShowScreenViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_movies_MoviesScreenViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_movies_MoviesScreenViewModel_HiltModules_BindsModule", "_com_google_jetstream_presentation_screens_movies_MoviesScreenViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\ScrapedMoviesStore_Factory.java": ["INSTANCE:com.google.jetstream.data.repositories.ScrapedMoviesStore_Factory.InstanceHolder", "<init>:com.google.jetstream.data.repositories.ScrapedMoviesStore_Factory.InstanceHolder", "ScrapedMoviesStore_Factory:com.google.jetstream.data.repositories", "create:com.google.jetstream.data.repositories.ScrapedMoviesStore_Factory", "<init>:com.google.jetstream.data.repositories.ScrapedMoviesStore_Factory", "get:com.google.jetstream.data.repositories.ScrapedMoviesStore_Factory", "newInstance:com.google.jetstream.data.repositories.ScrapedMoviesStore_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_shows_ShowScreenViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_shows_ShowScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_shows_ShowScreenViewModel_HiltModules_KeyModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\shows\\ShowsScreen.kt": ["ShowsScreen:com.google.jetstream.presentation.screens.shows"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\Episode.kt": ["getFormattedRating:com.google.jetstream.data.entities.Episode", "airDate:com.google.jetstream.data.entities.Episode", "getStillImageUrl:com.google.jetstream.data.entities.Episode", "getFormattedRuntime:com.google.jetstream.data.entities.Episode", "tvId:com.google.jetstream.data.entities.Episode", "episodeNumber:com.google.jetstream.data.entities.Episode", "watchProgress:com.google.jetstream.data.entities.Episode", "stillPath:com.google.jetstream.data.entities.Episode", "name:com.google.jetstream.data.entities.Episode", "seasonNumber:com.google.jetstream.data.entities.Episode", "currentPositionMs:com.google.jetstream.data.entities.Episode", "runtime:com.google.jetstream.data.entities.Episode", "voteAverage:com.google.jetstream.data.entities.Episode", "overview:com.google.jetstream.data.entities.Episode", "fileSizeBytes:com.google.jetstream.data.entities.Episode", "getFormattedAirDate:com.google.jetstream.data.entities.Episode", "fileName:com.google.jetstream.data.entities.Episode", "id:com.google.jetstream.data.entities.Episode", "videoUri:com.google.jetstream.data.entities.Episode", "durationMs:com.google.jetstream.data.entities.Episode", "Episode:com.google.jetstream.data.entities"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory.java": ["get:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.home", "create:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\HomeScreeViewModel_Factory.java": ["create:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_Factory", "get:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_Factory", "HomeScreeViewModel_Factory:com.google.jetstream.presentation.screens.home", "<init>:com.google.jetstream.presentation.screens.home.HomeScreeViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\favourites\\FavouriteScreenViewModel_HiltModules.java": ["FavouriteScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.favourites", "BindsModule:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules", "binds:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules.KeyModule", "KeyModule:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "WebDavBrowserViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\tvmaterial\\Dialog.kt": ["DescriptionPadding:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "backgroundColor:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "TitlePadding:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "iconContentColor:com.google.jetstream.tvmaterial.StandardDialogDefaults", "IconPadding:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "Level2:com.google.jetstream.tvmaterial.Elevation", "<init>:com.google.jetstream.tvmaterial.AnimationStage.Intro", "titleTextStyle:com.google.jetstream.tvmaterial.StandardDialogDefaults", "DialogPadding:com.google.jetstream.tvmaterial.StandardDialogDefaults", "<init>:com.google.jetstream.tvmaterial.MotionTokens", "textContentColor:com.google.jetstream.tvmaterial.StandardDialogDefaults", "shape:com.google.jetstream.tvmaterial.StandardDialogDefaults", "FullScreenDialogDefaults:com.google.jetstream.tvmaterial", "titleContentColor:com.google.jetstream.tvmaterial.StandardDialogDefaults", "containerColor:com.google.jetstream.tvmaterial.StandardDialogDefaults", "ButtonsCrossAxisSpacing:com.google.jetstream.tvmaterial.StandardDialogDefaults", "DialogMaxWidth:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "ButtonsFlowRowPadding:com.google.jetstream.tvmaterial.StandardDialogDefaults", "FullScreenDialog:com.google.jetstream.tvmaterial", "IconBottomSpacing:com.google.jetstream.tvmaterial.StandardDialogDefaults", "<init>:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "TitleMaxHeight:com.google.jetstream.tvmaterial.StandardDialogDefaults", "Level3:com.google.jetstream.tvmaterial.Elevation", "<init>:com.google.jetstream.tvmaterial.AnimationStage.Display", "DialogState:com.google.jetstream.tvmaterial", "descriptionContentColor:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "DialogMinWidth:com.google.jetstream.tvmaterial.StandardDialogDefaults", "StandardDialog:com.google.jetstream.tvmaterial", "ExitEasing:com.google.jetstream.tvmaterial.MotionTokens", "updateProgress:com.google.jetstream.tvmaterial.DialogState", "DialogMaxWidth:com.google.jetstream.tvmaterial.StandardDialogDefaults", "Level0:com.google.jetstream.tvmaterial.Elevation", "iconContentColor:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "Level4:com.google.jetstream.tvmaterial.Elevation", "Outro:com.google.jetstream.tvmaterial.AnimationStage", "<init>:com.google.jetstream.tvmaterial.StandardDialogDefaults", "titleContentColor:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "ButtonSpacing:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "<init>:com.google.jetstream.tvmaterial.DialogState", "TextPadding:com.google.jetstream.tvmaterial.StandardDialogDefaults", "titleTextStyle:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "textStyle:com.google.jetstream.tvmaterial.StandardDialogDefaults", "MotionTokens:com.google.jetstream.tvmaterial", "descriptionTextStyle:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "Level1:com.google.jetstream.tvmaterial.Elevation", "buttonsTextStyle:com.google.jetstream.tvmaterial.FullScreenDialogDefaults", "DialogFlowRow:com.google.jetstream.tvmaterial", "EnterEasing:com.google.jetstream.tvmaterial.MotionTokens", "<init>:com.google.jetstream.tvmaterial.Elevation", "buttonsTextStyle:com.google.jetstream.tvmaterial.StandardDialogDefaults", "Level5:com.google.jetstream.tvmaterial.Elevation", "Intro:com.google.jetstream.tvmaterial.AnimationStage", "StandardDialogDefaults:com.google.jetstream.tvmaterial", "<init>:com.google.jetstream.tvmaterial.AnimationStage.Outro", "ButtonsMainAxisSpacing:com.google.jetstream.tvmaterial.StandardDialogDefaults", "TonalElevation:com.google.jetstream.tvmaterial.StandardDialogDefaults", "Dialog:com.google.jetstream.tvmaterial", "Display:com.google.jetstream.tvmaterial.AnimationStage", "animationProgress:com.google.jetstream.tvmaterial.DialogState"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\favourites\\FilteredMoviesGrid.kt": ["FilteredMoviesGrid:com.google.jetstream.presentation.screens.favourites"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\MovieReviewsAndRatings.kt": ["reviewRating:com.google.jetstream.data.entities.MovieReviewsAndRatings", "reviewerIconUri:com.google.jetstream.data.entities.MovieReviewsAndRatings", "MovieReviewsAndRatings:com.google.jetstream.data.entities", "reviewerName:com.google.jetstream.data.entities.MovieReviewsAndRatings", "reviewCount:com.google.jetstream.data.entities.MovieReviewsAndRatings"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_dashboard_DashboardViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_dashboard_DashboardViewModel_HiltModules_KeyModule", "_com_google_jetstream_presentation_screens_dashboard_DashboardViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerSeeker.kt": ["VideoPlayerSeeker:com.google.jetstream.presentation.screens.videoPlayer.components"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavBrowserViewModel.kt": ["WebDavBrowserUiState:com.google.jetstream.presentation.screens.webdav", "loadDirectory:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "navigateUp:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "errorMessage:com.google.jetstream.presentation.screens.webdav.WebDavBrowserUiState", "breadcrumbs:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "isLoading:com.google.jetstream.presentation.screens.webdav.WebDavBrowserUiState", "successMessage:com.google.jetstream.presentation.screens.webdav.WebDavBrowserUiState", "lastModified:com.google.jetstream.presentation.screens.webdav.WebDavDirectoryItem", "savedPath:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "path:com.google.jetstream.presentation.screens.webdav.WebDavDirectoryItem", "currentPath:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "saveCurrentPath:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "clearMessages:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "navigateToDirectory:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "webDavService:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "name:com.google.jetstream.presentation.screens.webdav.WebDavDirectoryItem", "contentLength:com.google.jetstream.presentation.screens.webdav.WebDavDirectoryItem", "uiState:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "WebDavDirectoryItem:com.google.jetstream.presentation.screens.webdav", "repository:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel", "isDirectory:com.google.jetstream.presentation.screens.webdav.WebDavDirectoryItem", "WebDavBrowserViewModel:com.google.jetstream.presentation.screens.webdav", "directoryItems:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\util\\AssetsReader_Factory.java": ["get:com.google.jetstream.data.util.AssetsReader_Factory", "newInstance:com.google.jetstream.data.util.AssetsReader_Factory", "create:com.google.jetstream.data.util.AssetsReader_Factory", "<init>:com.google.jetstream.data.util.AssetsReader_Factory", "AssetsReader_Factory:com.google.jetstream.data.util"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavBrowserViewModel_HiltModules.java": ["BindsModule:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules", "binds:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules.BindsModule", "KeyModule:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules", "provide:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules.KeyModule", "WebDavBrowserViewModel_HiltModules:com.google.jetstream.presentation.screens.webdav"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerControlsIcon.kt": ["VideoPlayerControlsIcon:com.google.jetstream.presentation.screens.videoPlayer.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\MainActivity_GeneratedInjector.java": ["MainActivity_GeneratedInjector:com.google.jetstream", "injectMainActivity:com.google.jetstream.MainActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_videoPlayer_VideoPlayerScreenViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_videoPlayer_VideoPlayerScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_videoPlayer_VideoPlayerScreenViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetailsScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "MovieDetailsScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.movies", "keepFieldType:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\NextButton.kt": ["NextButton:com.google.jetstream.presentation.screens.videoPlayer.components"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\TvDataSource.kt": ["getBingeWatchDramaList:com.google.jetstream.data.repositories.TvDataSource", "TvDataSource:com.google.jetstream.data.repositories", "getTvShowList:com.google.jetstream.data.repositories.TvDataSource"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\database\\dao\\ScrapedItemDao_Impl.java": ["getById:com.google.jetstream.data.database.dao.ScrapedItemDao_Impl", "getRequiredConverters:com.google.jetstream.data.database.dao.ScrapedItemDao_Impl", "upsertAll:com.google.jetstream.data.database.dao.ScrapedItemDao_Impl", "clearAll:com.google.jetstream.data.database.dao.ScrapedItemDao_Impl", "ScrapedItemDao_Impl:com.google.jetstream.data.database.dao", "getAllByType:com.google.jetstream.data.database.dao.ScrapedItemDao_Impl", "<init>:com.google.jetstream.data.database.dao.ScrapedItemDao_Impl"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_RecentlyWatchedViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_home_RecentlyWatchedViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_RecentlyWatchedViewModel_HiltModules_KeyModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\CastAndCrewList.kt": ["CastAndCrewList:com.google.jetstream.presentation.screens.movies"], "src\\main\\java\\com\\google\\jetstream\\presentation\\theme\\Theme.kt": ["JetStreamTheme:com.google.jetstream.presentation.theme"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\search\\SearchScreenViewModel.kt": ["searchState:com.google.jetstream.presentation.screens.search.SearchScreenViewModel", "SearchScreenViewModel:com.google.jetstream.presentation.screens.search", "Done:com.google.jetstream.presentation.screens.search.SearchState", "<init>:com.google.jetstream.presentation.screens.search.SearchState.Searching", "movieList:com.google.jetstream.presentation.screens.search.SearchState.Done", "Searching:com.google.jetstream.presentation.screens.search.SearchState", "query:com.google.jetstream.presentation.screens.search.SearchScreenViewModel", "SearchState:com.google.jetstream.presentation.screens.search"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_dashboard_DashboardViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_dashboard_DashboardViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_dashboard_DashboardViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\RecentlyWatchedViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "RecentlyWatchedViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.home", "lazyClassKeyName:com.google.jetstream.presentation.screens.home.RecentlyWatchedViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\shows\\ShowScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "ShowScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.shows", "<init>:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavBrowserViewModel.kt": ["webDavService:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel", "WebDavBrowserViewModel:com.google.jetstream.presentation.screens.profile", "repository:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\dao\\WebDavConfigDao.kt": ["deleteConfigById:com.google.jetstream.data.database.dao.WebDavConfigDao", "deleteConfig:com.google.jetstream.data.database.dao.WebDavConfigDao", "insertConfig:com.google.jetstream.data.database.dao.WebDavConfigDao", "updateConfig:com.google.jetstream.data.database.dao.WebDavConfigDao", "WebDavConfigDao:com.google.jetstream.data.database.dao", "getAllConfigs:com.google.jetstream.data.database.dao.WebDavConfigDao", "getConfigById:com.google.jetstream.data.database.dao.WebDavConfigDao"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\VideoPlayerScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["VideoPlayerScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.videoPlayer", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\data\\models\\MoviesResponse.kt": ["subtitleUri:com.google.jetstream.data.models.MoviesResponseItem", "runtimeStr:com.google.jetstream.data.models.MoviesResponseItem", "ratingCount:com.google.jetstream.data.models.MoviesResponseItem", "runtimeMins:com.google.jetstream.data.models.MoviesResponseItem", "releaseDate:com.google.jetstream.data.models.MoviesResponseItem", "image_16_9:com.google.jetstream.data.models.MoviesResponseItem", "directors:com.google.jetstream.data.models.MoviesResponseItem", "title:com.google.jetstream.data.models.MoviesResponseItem", "year:com.google.jetstream.data.models.MoviesResponseItem", "plot:com.google.jetstream.data.models.MoviesResponseItem", "metaCriticRating:com.google.jetstream.data.models.MoviesResponseItem", "contentRating:com.google.jetstream.data.models.MoviesResponseItem", "image_2_3:com.google.jetstream.data.models.MoviesResponseItem", "fullTitle:com.google.jetstream.data.models.MoviesResponseItem", "rating:com.google.jetstream.data.models.MoviesResponseItem", "videoUri:com.google.jetstream.data.models.MoviesResponseItem", "MoviesResponseItem:com.google.jetstream.data.models", "id:com.google.jetstream.data.models.MoviesResponseItem", "genres:com.google.jetstream.data.models.MoviesResponseItem", "rank:com.google.jetstream.data.models.MoviesResponseItem", "stars:com.google.jetstream.data.models.MoviesResponseItem", "rankUpDown:com.google.jetstream.data.models.MoviesResponseItem"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movietype\\MovieTypeListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "MovieTypeListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.movietype", "<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\search\\SearchScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "SearchScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.search", "keepFieldType:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoryMovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "CategoryMovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.categories", "lazyClassKeyName:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movietype\\MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.movietype", "<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "INSTANCE:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "DashboardViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.dashboard", "get:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardTopBarItemIndicator.kt": ["DashboardTopBarItemIndicator:com.google.jetstream.presentation.screens.dashboard"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreen.kt": ["MoviesScreen:com.google.jetstream.presentation.screens.movies"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardViewModel_HiltModules.java": ["DashboardViewModel_HiltModules:com.google.jetstream.presentation.screens.dashboard", "binds:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules.KeyModule", "KeyModule:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules", "BindsModule:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["DashboardViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.dashboard", "<init>:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\AccountsSectionDeleteDialog.kt": ["AccountsSectionDeleteDialog:com.google.jetstream.presentation.screens.profile"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\VideoPlayerScreenViewModel_Factory.java": ["VideoPlayerScreenViewModel_Factory:com.google.jetstream.presentation.screens.videoPlayer", "get:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_Factory", "create:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavConfigDialog.kt": ["WebDavConfigDialog:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\MovieRepository.kt": ["getMovieCategoryDetails:com.google.jetstream.data.repositories.MovieRepository", "getMoviesWithLongThumbnail:com.google.jetstream.data.repositories.MovieRepository", "getNowPlayingMovies:com.google.jetstream.data.repositories.MovieRepository", "getFavouriteMovies:com.google.jetstream.data.repositories.MovieRepository", "getMovieDetails:com.google.jetstream.data.repositories.MovieRepository", "searchMovies:com.google.jetstream.data.repositories.MovieRepository", "getBingeWatchDramas:com.google.jetstream.data.repositories.MovieRepository", "getMovieCategories:com.google.jetstream.data.repositories.MovieRepository", "getTrendingMovies:com.google.jetstream.data.repositories.MovieRepository", "getMovies:com.google.jetstream.data.repositories.MovieRepository", "getTVShows:com.google.jetstream.data.repositories.MovieRepository", "getFeaturedMovies:com.google.jetstream.data.repositories.MovieRepository", "getPopularFilmsThisWeek:com.google.jetstream.data.repositories.MovieRepository", "getTop10Movies:com.google.jetstream.data.repositories.MovieRepository", "MovieRepository:com.google.jetstream.data.repositories"], "src\\main\\java\\com\\google\\jetstream\\presentation\\common\\MovieCard.kt": ["MovieCard:com.google.jetstream.presentation.common"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\VideoPlayerScreenViewModel_HiltModules.java": ["binds:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules.KeyModule", "VideoPlayerScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.videoPlayer", "KeyModule:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules", "BindsModule:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_ScrapedMoviesViewModel_HiltModules_BindsModule.java": ["_com_google_jetstream_presentation_screens_home_ScrapedMoviesViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_ScrapedMoviesViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedTvViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "ScrapedTvViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.home", "<init>:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedTvViewModel_Factory.java": ["create:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_Factory", "ScrapedTvViewModel_Factory:com.google.jetstream.presentation.screens.home", "<init>:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_Factory", "get:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\data\\models\\MovieCastResponse.kt": ["MovieCastResponseItem:com.google.jetstream.data.models", "characterName:com.google.jetstream.data.models.MovieCastResponseItem", "realName:com.google.jetstream.data.models.MovieCastResponseItem", "id:com.google.jetstream.data.models.MovieCastResponseItem", "avatarUrl:com.google.jetstream.data.models.MovieCastResponseItem"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_HomeScreeViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_home_HomeScreeViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_HomeScreeViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\movietype\\MovieTypeListScreenViewModel_HiltModules.java": ["BindsModule:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules", "MovieTypeListScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.movietype", "binds:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\CachedDataReader.kt": ["readMovieData:com.google.jetstream.data.repositories", "MovieDataReader:com.google.jetstream.data.repositories", "readMovieCastData:com.google.jetstream.data.repositories", "read:com.google.jetstream.data.repositories.CachedDataReader", "CachedDataReader:com.google.jetstream.data.repositories", "readMovieCategoryData:com.google.jetstream.data.repositories"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_home_ScrapedMoviesViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_home_ScrapedMoviesViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_home_ScrapedMoviesViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\di\\DatabaseModule_ProvideResourceDirectoryDaoFactory.java": ["create:com.google.jetstream.di.DatabaseModule_ProvideResourceDirectoryDaoFactory", "provideResourceDirectoryDao:com.google.jetstream.di.DatabaseModule_ProvideResourceDirectoryDaoFactory", "DatabaseModule_ProvideResourceDirectoryDaoFactory:com.google.jetstream.di", "<init>:com.google.jetstream.di.DatabaseModule_ProvideResourceDirectoryDaoFactory", "get:com.google.jetstream.di.DatabaseModule_ProvideResourceDirectoryDaoFactory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\SearchHistorySection.kt": ["SearchHistorySection:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\data\\services\\TvPlaybackService.kt": ["savePlaybackProgress:com.google.jetstream.data.services.TvPlaybackService", "getPlaybackInfo:com.google.jetstream.data.services.TvPlaybackService", "<init>:com.google.jetstream.data.services.TvPlaybackService.Companion", "Companion:com.google.jetstream.data.services.TvPlaybackService", "isResuming:com.google.jetstream.data.services.TvPlaybackService.PlaybackInfo", "PlaybackInfo:com.google.jetstream.data.services.TvPlaybackService", "TvPlaybackService:com.google.jetstream.data.services", "episode:com.google.jetstream.data.services.TvPlaybackService.PlaybackInfo", "startPositionMs:com.google.jetstream.data.services.TvPlaybackService.PlaybackInfo"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedMoviesViewModel_HiltModules.java": ["ScrapedMoviesViewModel_HiltModules:com.google.jetstream.presentation.screens.home", "BindsModule:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules", "binds:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\MovieCast.kt": ["realName:com.google.jetstream.data.entities.MovieCast", "characterName:com.google.jetstream.data.entities.MovieCast", "toMovieCast:com.google.jetstream.data.entities", "avatarUrl:com.google.jetstream.data.entities.MovieCast", "MovieCast:com.google.jetstream.data.entities", "id:com.google.jetstream.data.entities.MovieCast"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_favourites_FavouriteScreenViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_favourites_FavouriteScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_favourites_FavouriteScreenViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "MoviesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.movies"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory.java": ["ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.home", "create:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\RememberPlayer.kt": ["rememberPlayer:com.google.jetstream.presentation.screens.videoPlayer.components"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\MovieCastDataSource.kt": ["getMovieCastList:com.google.jetstream.data.repositories.MovieCastDataSource", "MovieCastDataSource:com.google.jetstream.data.repositories"], "src\\main\\java\\com\\google\\jetstream\\presentation\\common\\MediaCard.kt": ["MediaCard:com.google.jetstream.presentation.common"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavBrowserViewModel_HiltModules.java": ["provide:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules.KeyModule", "WebDavBrowserViewModel_HiltModules:com.google.jetstream.presentation.screens.profile", "binds:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules.BindsModule", "BindsModule:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.profile.WebDavBrowserViewModel_HiltModules"], "src\\main\\java\\com\\google\\jetstream\\presentation\\utils\\Colors.kt": ["Purple300:com.google.jetstream.presentation.utils", "DeepOrange300:com.google.jetstream.presentation.utils", "LightBlue300:com.google.jetstream.presentation.utils", "Green300:com.google.jetstream.presentation.utils", "Cyan300:com.google.jetstream.presentation.utils", "BlueGray300:com.google.jetstream.presentation.utils", "DeepPurple300:com.google.jetstream.presentation.utils", "Pink300:com.google.jetstream.presentation.utils", "Red300:com.google.jetstream.presentation.utils", "Blue300:com.google.jetstream.presentation.utils", "ourColors:com.google.jetstream.presentation.utils", "Indigo300:com.google.jetstream.presentation.utils", "Coral:com.google.jetstream.presentation.utils", "Yellow300:com.google.jetstream.presentation.utils", "Orange300:com.google.jetstream.presentation.utils", "Brown300:com.google.jetstream.presentation.utils", "Lime300:com.google.jetstream.presentation.utils", "LightYellow:com.google.jetstream.presentation.utils", "Amber300:com.google.jetstream.presentation.utils", "Teal300:com.google.jetstream.presentation.utils", "Gray300:com.google.jetstream.presentation.utils", "LightGreen300:com.google.jetstream.presentation.utils"], "src\\main\\java\\com\\google\\jetstream\\data\\util\\StringConstants.kt": ["VideoPlayerControlRepeatOne:com.google.jetstream.data.util.StringConstants.Composable", "SubtitlesSectionLanguageValue:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "HelpAndSupportSectionContactValue:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "InvalidCategoryId:com.google.jetstream.data.util.StringConstants.Exceptions", "CategoryDetailsFailureSubject:com.google.jetstream.data.util.StringConstants.Composable", "VideoPlayerControlSkipPreviousButton:com.google.jetstream.data.util.StringConstants.Composable", "WorldWideGrossDefault:com.google.jetstream.data.util.StringConstants.Movie", "MovieCast:com.google.jetstream.data.util.StringConstants.Assets", "<init>:com.google.jetstream.data.util.StringConstants.Movie.Reviewer", "HelpAndSupportSectionTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "StringConstants:com.google.jetstream.data.util", "MostPopularTVShows:com.google.jetstream.data.util.StringConstants.Assets", "SubtitlesSectionTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "FreshTomatoes:com.google.jetstream.data.util.StringConstants.Movie.Reviewer", "SubtitlesSectionLanguageItem:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "SearchHistorySectionTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "AboutSectionTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "AccountsSelectionDeleteAccountTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "HelpAndSupportSectionListItemIconDescription:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "InTheaters:com.google.jetstream.data.util.StringConstants.Assets", "SearchHistoryClearAll:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "LanguageSectionItems:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "Top250Movies:com.google.jetstream.data.util.StringConstants.Assets", "reviewerName:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "movieDetailsScreenSimilarTo:com.google.jetstream.data.util.StringConstants.Composable", "VideoPlayerControlRepeatButton:com.google.jetstream.data.util.StringConstants.Composable", "MoviesFailureSubject:com.google.jetstream.data.util.StringConstants.Composable", "UnknownException:com.google.jetstream.data.util.StringConstants.Exceptions", "VideoPlayerControlRepeatAll:com.google.jetstream.data.util.StringConstants.Composable", "<init>:com.google.jetstream.data.util.StringConstants.Movie", "BingeWatchDramasTitle:com.google.jetstream.data.util.StringConstants.Composable", "Reviewer:com.google.jetstream.data.util.StringConstants.Movie", "BrandLogoImage:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "reviewCount:com.google.jetstream.data.util.StringConstants.Composable", "HomeScreenNowPlayingMoviesTitle:com.google.jetstream.data.util.StringConstants.Composable", "VideoPlayerControlClosedCaptionsButton:com.google.jetstream.data.util.StringConstants.Composable", "AccountsSelectionSwitchAccountsTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "FreshTomatoesImageUrl:com.google.jetstream.data.util.StringConstants.Movie.Reviewer", "Movie:com.google.jetstream.data.util.StringConstants", "DashboardSearchButton:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "<init>:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "<init>:com.google.jetstream.data.util.StringConstants", "SampleSearchHistory:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "SubtitlesSectionSubtitlesItem:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "<init>:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "PopularFilmsThisWeekTitle:com.google.jetstream.data.util.StringConstants.Composable", "ImageUrl:com.google.jetstream.data.util.StringConstants.Movie.Reviewer", "<init>:com.google.jetstream.data.util.StringConstants.Composable", "VideoPlayerControlForward:com.google.jetstream.data.util.StringConstants.Composable", "Exceptions:com.google.jetstream.data.util.StringConstants", "image:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "AboutSectionDescription:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "VideoPlayerControlSkipNextButton:com.google.jetstream.data.util.StringConstants.Composable", "FilterSelected:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "HelpAndSupportSectionPrivacyItem:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "VideoPlayerControlRepeatNone:com.google.jetstream.data.util.StringConstants.Composable", "HomeScreenTrendingTitle:com.google.jetstream.data.util.StringConstants.Composable", "<init>:com.google.jetstream.data.util.StringConstants.Assets", "VideoPlayerControlPlayPauseButton:com.google.jetstream.data.util.StringConstants.Composable", "DefaultRating:com.google.jetstream.data.util.StringConstants.Movie.Reviewer", "ContentDescription:com.google.jetstream.data.util.StringConstants.Composable", "AccountsSelectionLogOut:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "HelpAndSupportSectionFAQItem:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "<init>:com.google.jetstream.data.util.StringConstants.Exceptions", "MovieCategories:com.google.jetstream.data.util.StringConstants.Assets", "AboutSectionAppVersionTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "VideoPlayerControlSettingsButton:com.google.jetstream.data.util.StringConstants.Composable", "UserAvatar:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "AccountsSelectionViewSubscriptionsTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "moviePoster:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "BudgetDefault:com.google.jetstream.data.util.StringConstants.Movie", "ReviewerName:com.google.jetstream.data.util.StringConstants.Movie.Reviewer", "AccountsSelectionSwitchAccountsEmail:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "AccountsSelectionChangePasswordValue:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "Composable:com.google.jetstream.data.util.StringConstants", "MostPopularMovies:com.google.jetstream.data.util.StringConstants.Assets", "Placeholders:com.google.jetstream.data.util.StringConstants.Composable", "LanguageSectionTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "MovieDetailsFailureSubject:com.google.jetstream.data.util.StringConstants.Composable", "AccountsSelectionAddNewAccountTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "AccountsSelectionChangePasswordTitle:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "DefaultCount:com.google.jetstream.data.util.StringConstants.Movie.Reviewer", "Assets:com.google.jetstream.data.util.StringConstants", "StatusReleased:com.google.jetstream.data.util.StringConstants.Movie", "HelpAndSupportSectionContactItem:com.google.jetstream.data.util.StringConstants.Composable.Placeholders", "MoviesCarousel:com.google.jetstream.data.util.StringConstants.Composable.ContentDescription", "VideoPlayerControlPlaylistButton:com.google.jetstream.data.util.StringConstants.Composable"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_movies_MovieDetailsScreenViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_movies_MovieDetailsScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_movies_MovieDetailsScreenViewModel_HiltModules_KeyModule"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\MovieDataSource.kt": ["getMovieList:com.google.jetstream.data.repositories.MovieDataSource", "getFavoriteMovieList:com.google.jetstream.data.repositories.MovieDataSource", "MovieDataSource:com.google.jetstream.data.repositories", "getTrendingMovieList:com.google.jetstream.data.repositories.MovieDataSource", "getPopularFilmThisWeek:com.google.jetstream.data.repositories.MovieDataSource", "getNowPlayingMovieList:com.google.jetstream.data.repositories.MovieDataSource", "getFeaturedMovieList:com.google.jetstream.data.repositories.MovieDataSource", "getTop10MovieList:com.google.jetstream.data.repositories.MovieDataSource"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\shows\\ShowScreenViewModel.kt": ["tvShowList:com.google.jetstream.presentation.screens.shows.ShowScreenUiState.Ready", "Ready:com.google.jetstream.presentation.screens.shows.ShowScreenUiState", "<init>:com.google.jetstream.presentation.screens.shows.ShowScreenUiState.Loading", "ShowScreenViewModel:com.google.jetstream.presentation.screens.shows", "Loading:com.google.jetstream.presentation.screens.shows.ShowScreenUiState", "bingeWatchDramaList:com.google.jetstream.presentation.screens.shows.ShowScreenUiState.Ready", "uiState:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel", "ShowScreenUiState:com.google.jetstream.presentation.screens.shows"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\EpisodeList.kt": ["EpisodeList:com.google.jetstream.presentation.screens.movies"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\MovieRepositoryImpl.kt": ["getMoviesWithLongThumbnail:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getNowPlayingMovies:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getFavouriteMovies:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getMovieDetails:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getFeaturedMovies:com.google.jetstream.data.repositories.MovieRepositoryImpl", "MovieRepositoryImpl:com.google.jetstream.data.repositories", "searchMovies:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getBingeWatchDramas:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getTop10Movies:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getTVShows:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getMovies:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getMovieCategories:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getTrendingMovies:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getPopularFilmsThisWeek:com.google.jetstream.data.repositories.MovieRepositoryImpl", "getMovieCategoryDetails:com.google.jetstream.data.repositories.MovieRepositoryImpl"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavBrowserSection.kt": ["name:com.google.jetstream.presentation.screens.profile.ResourceDirectory", "ResourceDirectory:com.google.jetstream.presentation.screens.profile", "id:com.google.jetstream.presentation.screens.profile.ResourceDirectory", "serverName:com.google.jetstream.presentation.screens.profile.ResourceDirectory", "path:com.google.jetstream.presentation.screens.profile.ResourceDirectory", "WebDavBrowserSection:com.google.jetstream.presentation.screens.profile"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.webdav.WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "WebDavBrowserViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.webdav"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\VideoPlayerScreenViewModel.kt": ["addToRecentlyWatched:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel", "saveWatchProgress:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel", "Loading:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState", "headers:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel", "saveEpisodeWatchProgress:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel", "VideoPlayerScreenViewModel:com.google.jetstream.presentation.screens.videoPlayer", "episodeId:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState.Done", "startPositionMs:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState.Done", "uiState:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel", "saveCurrentEpisodeProgress:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel", "movieDetails:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState.Done", "VideoPlayerScreenUiState:com.google.jetstream.presentation.screens.videoPlayer", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState.Error", "Done:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState", "<init>:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState.Loading", "Error:com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\Movie.kt": ["releaseDate:com.google.jetstream.data.entities.Movie", "seasonNumber:com.google.jetstream.data.entities.Movie", "isTV:com.google.jetstream.data.entities.Movie", "posterUri:com.google.jetstream.data.entities.Movie", "name:com.google.jetstream.data.entities.Movie", "Standard:com.google.jetstream.data.entities.ThumbnailType", "<init>:com.google.jetstream.data.entities.ThumbnailType.Standard", "videoUri:com.google.jetstream.data.entities.Movie", "Long:com.google.jetstream.data.entities.ThumbnailType", "ThumbnailType:com.google.jetstream.data.entities", "id:com.google.jetstream.data.entities.Movie", "durationMs:com.google.jetstream.data.entities.Movie", "Movie:com.google.jetstream.data.entities", "rating:com.google.jetstream.data.entities.Movie", "subtitleUri:com.google.jetstream.data.entities.Movie", "description:com.google.jetstream.data.entities.Movie", "toMovie:com.google.jetstream.data.entities", "watchProgress:com.google.jetstream.data.entities.Movie", "episodeNumber:com.google.jetstream.data.entities.Movie", "currentPositionMs:com.google.jetstream.data.entities.Movie", "<init>:com.google.jetstream.data.entities.ThumbnailType.Long", "episodeId:com.google.jetstream.data.entities.Movie"], "src\\main\\java\\com\\google\\jetstream\\presentation\\theme\\Type.kt": ["LexendExa:com.google.jetstream.presentation.theme", "Typography:com.google.jetstream.presentation.theme"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\entities\\ScrapedItemEntity.kt": ["ScrapedItemEntity:com.google.jetstream.data.database.entities", "posterUri:com.google.jetstream.data.database.entities.ScrapedItemEntity", "type:com.google.jetstream.data.database.entities.ScrapedItemEntity", "description:com.google.jetstream.data.database.entities.ScrapedItemEntity", "pgRating:com.google.jetstream.data.database.entities.ScrapedItemEntity", "duration:com.google.jetstream.data.database.entities.ScrapedItemEntity", "title:com.google.jetstream.data.database.entities.ScrapedItemEntity", "sourcePath:com.google.jetstream.data.database.entities.ScrapedItemEntity", "screenplay:com.google.jetstream.data.database.entities.ScrapedItemEntity", "director:com.google.jetstream.data.database.entities.ScrapedItemEntity", "availableSeasons:com.google.jetstream.data.database.entities.ScrapedItemEntity", "backdropUri:com.google.jetstream.data.database.entities.ScrapedItemEntity", "releaseDate:com.google.jetstream.data.database.entities.ScrapedItemEntity", "music:com.google.jetstream.data.database.entities.ScrapedItemEntity", "rating:com.google.jetstream.data.database.entities.ScrapedItemEntity", "categories:com.google.jetstream.data.database.entities.ScrapedItemEntity", "createdAt:com.google.jetstream.data.database.entities.ScrapedItemEntity", "id:com.google.jetstream.data.database.entities.ScrapedItemEntity", "castAndCrew:com.google.jetstream.data.database.entities.ScrapedItemEntity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\database\\JetStreamDatabase_Impl.java": ["getAutoMigrations:com.google.jetstream.data.database.JetStreamDatabase_Impl", "<init>:com.google.jetstream.data.database.JetStreamDatabase_Impl", "resourceDirectoryDao:com.google.jetstream.data.database.JetStreamDatabase_Impl", "recentlyWatchedDao:com.google.jetstream.data.database.JetStreamDatabase_Impl", "getRequiredTypeConverters:com.google.jetstream.data.database.JetStreamDatabase_Impl", "webDavConfigDao:com.google.jetstream.data.database.JetStreamDatabase_Impl", "createInvalidationTracker:com.google.jetstream.data.database.JetStreamDatabase_Impl", "clearAllTables:com.google.jetstream.data.database.JetStreamDatabase_Impl", "JetStreamDatabase_Impl:com.google.jetstream.data.database", "scrapedItemDao:com.google.jetstream.data.database.JetStreamDatabase_Impl", "createOpenHelper:com.google.jetstream.data.database.JetStreamDatabase_Impl", "getRequiredAutoMigrationSpecs:com.google.jetstream.data.database.JetStreamDatabase_Impl"], "src\\main\\java\\com\\google\\jetstream\\di\\DatabaseModule.kt": ["provideScrapedItemDao:com.google.jetstream.di.DatabaseModule", "provideJetStreamDatabase:com.google.jetstream.di.DatabaseModule", "DatabaseModule:com.google.jetstream.di", "provideWebDavConfigDao:com.google.jetstream.di.DatabaseModule", "<init>:com.google.jetstream.di.DatabaseModule", "provideResourceDirectoryDao:com.google.jetstream.di.DatabaseModule", "provideRecentlyWatchedDao:com.google.jetstream.di.DatabaseModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavConfigViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["WebDavConfigViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.google.jetstream.presentation.screens.webdav", "keepFieldType:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\dashboard\\DashboardViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "DashboardViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.dashboard"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoryMovieListScreenViewModel.kt": ["Done:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState", "<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState.Error", "CategoryMovieListScreenViewModel:com.google.jetstream.presentation.screens.categories", "Error:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState", "CategoryMovieListScreenUiState:com.google.jetstream.presentation.screens.categories", "uiState:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel", "Loading:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState", "movieCategoryDetails:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState.Done", "<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState.Loading"], "src\\main\\java\\com\\google\\jetstream\\data\\remote\\TmdbService.kt": ["CreditsResponse:com.google.jetstream.data.remote.TmdbService", "PagedResponse:com.google.jetstream.data.remote.TmdbService", "releaseDates:com.google.jetstream.data.remote.TmdbService.CountryRelease", "getReleaseCertification:com.google.jetstream.data.remote.TmdbService", "name:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "name:com.google.jetstream.data.remote.TmdbService.SimilarItem", "TvDetailsResponse:com.google.jetstream.data.remote.TmdbService", "name:com.google.jetstream.data.remote.TmdbService.CrewItem", "title:com.google.jetstream.data.remote.TmdbService.SimilarItem", "IMAGE_BASE_W500:com.google.jetstream.data.remote.TmdbService", "results:com.google.jetstream.data.remote.TmdbService.ContentRatingsResponse", "posterPath:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "getMovieDetails:com.google.jetstream.data.remote.TmdbService", "name:com.google.jetstream.data.remote.TmdbService.Creator", "airDate:com.google.jetstream.data.remote.TmdbService.SeasonDetailsResponse", "MovieDetailsResponse:com.google.jetstream.data.remote.TmdbService", "profilePath:com.google.jetstream.data.remote.TmdbService.CastItem", "budget:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "firstAirDate:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "page:com.google.jetstream.data.remote.TmdbService.PagedResponse", "Genre:com.google.jetstream.data.remote.TmdbService", "CountryRelease:com.google.jetstream.data.remote.TmdbService", "cast:com.google.jetstream.data.remote.TmdbService.CreditsResponse", "id:com.google.jetstream.data.remote.TmdbService.Creator", "ReleaseDateItem:com.google.jetstream.data.remote.TmdbService", "name:com.google.jetstream.data.remote.TmdbService.SeasonDetailsResponse", "country:com.google.jetstream.data.remote.TmdbService.ContentRating", "id:com.google.jetstream.data.remote.TmdbService.Genre", "numberOfSeasons:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "revenue:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "lastAirDate:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "profilePath:com.google.jetstream.data.remote.TmdbService.CrewItem", "id:com.google.jetstream.data.remote.TmdbService.CrewItem", "SeasonDetailsResponse:com.google.jetstream.data.remote.TmdbService", "voteAverage:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "country:com.google.jetstream.data.remote.TmdbService.CountryRelease", "originalLanguage:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "overview:com.google.jetstream.data.remote.TmdbService.SeasonDetailsResponse", "numberOfEpisodes:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "id:com.google.jetstream.data.remote.TmdbService.CastItem", "getTvContentRating:com.google.jetstream.data.remote.TmdbService", "EpisodeItem:com.google.jetstream.data.remote.TmdbService", "ReleaseDatesResponse:com.google.jetstream.data.remote.TmdbService", "character:com.google.jetstream.data.remote.TmdbService.CastItem", "IMAGE_BASE_W780:com.google.jetstream.data.remote.TmdbService", "certification:com.google.jetstream.data.remote.TmdbService.ReleaseDateItem", "id:com.google.jetstream.data.remote.TmdbService.SimilarItem", "<init>:com.google.jetstream.data.remote.TmdbService", "releaseDate:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "title:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "crew:com.google.jetstream.data.remote.TmdbService.CreditsResponse", "id:com.google.jetstream.data.remote.TmdbService.SeasonDetailsResponse", "name:com.google.jetstream.data.remote.TmdbService.Genre", "status:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "CastItem:com.google.jetstream.data.remote.TmdbService", "name:com.google.jetstream.data.remote.TmdbService.CastItem", "ContentRating:com.google.jetstream.data.remote.TmdbService", "overview:com.google.jetstream.data.remote.TmdbService.SimilarItem", "genres:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "runtime:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "episodeNumber:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "getTvDetails:com.google.jetstream.data.remote.TmdbService", "runtime:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "id:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "seasonNumber:com.google.jetstream.data.remote.TmdbService.SeasonDetailsResponse", "id:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "SimilarItem:com.google.jetstream.data.remote.TmdbService", "getSimilar:com.google.jetstream.data.remote.TmdbService", "ContentRatingsResponse:com.google.jetstream.data.remote.TmdbService", "posterPath:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "overview:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "status:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "name:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "stillPath:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "originalLanguage:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "voteAverage:com.google.jetstream.data.remote.TmdbService.SimilarItem", "TmdbService:com.google.jetstream.data.remote", "results:com.google.jetstream.data.remote.TmdbService.PagedResponse", "airDate:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "voteAverage:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "getCredits:com.google.jetstream.data.remote.TmdbService", "backdropPath:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "Creator:com.google.jetstream.data.remote.TmdbService", "overview:com.google.jetstream.data.remote.TmdbService.MovieDetailsResponse", "releaseDate:com.google.jetstream.data.remote.TmdbService.SimilarItem", "createdBy:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "overview:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "results:com.google.jetstream.data.remote.TmdbService.ReleaseDatesResponse", "episodes:com.google.jetstream.data.remote.TmdbService.SeasonDetailsResponse", "getTvCredits:com.google.jetstream.data.remote.TmdbService", "getTvSimilar:com.google.jetstream.data.remote.TmdbService", "job:com.google.jetstream.data.remote.TmdbService.CrewItem", "CrewItem:com.google.jetstream.data.remote.TmdbService", "posterPath:com.google.jetstream.data.remote.TmdbService.SimilarItem", "getTvSeasonDetails:com.google.jetstream.data.remote.TmdbService", "episodeRunTime:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "id:com.google.jetstream.data.remote.TmdbService.EpisodeItem", "backdropPath:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "voteAverage:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "genres:com.google.jetstream.data.remote.TmdbService.TvDetailsResponse", "posterPath:com.google.jetstream.data.remote.TmdbService.SeasonDetailsResponse", "rating:com.google.jetstream.data.remote.TmdbService.ContentRating"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\favourites\\MovieFilterChipRow.kt": ["MovieFilterChipRow:com.google.jetstream.presentation.screens.favourites"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\LanguageSection.kt": ["LanguageSection:com.google.jetstream.presentation.screens.profile"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavConfigViewModel_Factory.java": ["create:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_Factory", "get:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_Factory", "WebDavConfigViewModel_Factory:com.google.jetstream.presentation.screens.webdav", "newInstance:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_Factory", "<init>:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\shows\\ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.shows", "INSTANCE:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "provide:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoryMovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["keepFieldType:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "CategoryMovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.categories"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_webdav_WebDavConfigViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_webdav_WebDavConfigViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_webdav_WebDavConfigViewModel_HiltModules_KeyModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieDetailsScreenViewModel.kt": ["EpisodesUiState:com.google.jetstream.presentation.screens.movies", "startTvPlayback:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel", "Loading:com.google.jetstream.presentation.screens.movies.EpisodesUiState", "MovieDetailsScreenUiState:com.google.jetstream.presentation.screens.movies", "Loading:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState", "episodes:com.google.jetstream.presentation.screens.movies.EpisodesUiState.Success", "<init>:com.google.jetstream.presentation.screens.movies.EpisodesUiState", "<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState.Error", "<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState", "loadEpisodes:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel", "sourceInfoEpisode:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel", "MovieDetailsScreenViewModel:com.google.jetstream.presentation.screens.movies", "fileSizeBytes:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState.Done", "Error:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState", "message:com.google.jetstream.presentation.screens.movies.EpisodesUiState.Error", "<init>:com.google.jetstream.presentation.screens.movies.EpisodesUiState.Loading", "episodesState:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel", "clearEpisodes:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel", "movieDetails:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState.Done", "Done:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState", "Success:com.google.jetstream.presentation.screens.movies.EpisodesUiState", "uiState:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel", "<init>:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState.Loading", "recentlyWatched:com.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState.Done", "Error:com.google.jetstream.presentation.screens.movies.EpisodesUiState"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_movies_MoviesScreenViewModel_HiltModules_KeyModule.java": ["_com_google_jetstream_presentation_screens_movies_MoviesScreenViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_movies_MoviesScreenViewModel_HiltModules_KeyModule"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavConfigViewModel.kt": ["WebDavConfigViewModel:com.google.jetstream.presentation.screens.webdav", "serverUrl:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "errorMessage:com.google.jetstream.presentation.screens.webdav.WebDavConfigUiState", "saveConfig:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "WebDavConfigUiState:com.google.jetstream.presentation.screens.webdav", "updateUsername:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "displayName:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "isConfigValid:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "clearMessages:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "updateServerUrl:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "updateDisplayName:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "updatePassword:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "password:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "uiState:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "isLoading:com.google.jetstream.presentation.screens.webdav.WebDavConfigUiState", "testConnection:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "connectionStatus:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "clearConfig:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "username:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel", "successMessage:com.google.jetstream.presentation.screens.webdav.WebDavConfigUiState"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\movietype\\MovieTypeListScreenViewModel_Factory.java": ["<init>:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_Factory", "MovieTypeListScreenViewModel_Factory:com.google.jetstream.presentation.screens.movietype", "create:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_Factory", "get:com.google.jetstream.presentation.screens.movietype.MovieTypeListScreenViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerState.kt": ["showControls:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerState", "rememberVideoPlayerState:com.google.jetstream.presentation.screens.videoPlayer.components", "hideControls:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerState", "observe:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerState", "VideoPlayerState:com.google.jetstream.presentation.screens.videoPlayer.components", "isControlsVisible:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerState"], "src\\main\\java\\com\\google\\jetstream\\presentation\\utils\\Padding.kt": ["Padding:com.google.jetstream.presentation.utils", "top:com.google.jetstream.presentation.utils.Padding", "end:com.google.jetstream.presentation.utils.Padding", "start:com.google.jetstream.presentation.utils.Padding", "bottom:com.google.jetstream.presentation.utils.Padding"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerPulse.kt": ["FORWARD:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse.Type", "rememberVideoPlayerPulseState:com.google.jetstream.presentation.screens.videoPlayer.components", "<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse.Type.NONE", "VideoPlayerPulse:com.google.jetstream.presentation.screens.videoPlayer.components", "VideoPlayerPulseState:com.google.jetstream.presentation.screens.videoPlayer.components", "type:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulseState", "NONE:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse.Type", "<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse.Type.BACK", "Type:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse", "BACK:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse.Type", "<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse.Type.FORWARD", "setType:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulseState", "observe:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulseState", "<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulse", "<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerPulseState"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\SeasonSelector.kt": ["episodeCount:com.google.jetstream.presentation.screens.movies.Season", "Season:com.google.jetstream.presentation.screens.movies", "SeasonSelector:com.google.jetstream.presentation.screens.movies", "displayName:com.google.jetstream.presentation.screens.movies.Season", "number:com.google.jetstream.presentation.screens.movies.Season"], "src\\main\\java\\com\\google\\jetstream\\data\\entities\\MovieCategoryDetails.kt": ["name:com.google.jetstream.data.entities.MovieCategoryDetails", "movies:com.google.jetstream.data.entities.MovieCategoryDetails", "MovieCategoryDetails:com.google.jetstream.data.entities", "id:com.google.jetstream.data.entities.MovieCategoryDetails"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\webdav\\WebDavService_Factory.java": ["get:com.google.jetstream.data.webdav.WebDavService_Factory", "WebDavService_Factory:com.google.jetstream.data.webdav", "<init>:com.google.jetstream.data.webdav.WebDavService_Factory", "newInstance:com.google.jetstream.data.webdav.WebDavService_Factory", "<init>:com.google.jetstream.data.webdav.WebDavService_Factory.InstanceHolder", "create:com.google.jetstream.data.webdav.WebDavService_Factory", "INSTANCE:com.google.jetstream.data.webdav.WebDavService_Factory.InstanceHolder"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\database\\dao\\RecentlyWatchedDao_Impl.java": ["deleteOldest:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "<init>:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "clearAll:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "getCount:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "deleteByMovieId:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "getRequiredConverters:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "getRecentlyWatched:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "RecentlyWatchedDao_Impl:com.google.jetstream.data.database.dao", "insertOrUpdate:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl", "getByMovieId:com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl"], "src\\main\\java\\com\\google\\jetstream\\presentation\\utils\\GradientBg.kt": ["GradientBg:com.google.jetstream.presentation.utils", "pairs:com.google.jetstream.presentation.utils"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreenViewModel_Factory.java": ["get:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_Factory", "newInstance:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_Factory", "MoviesScreenViewModel_Factory:com.google.jetstream.presentation.screens.movies", "<init>:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_Factory", "create:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedTvViewModel.kt": ["shows:com.google.jetstream.presentation.screens.home.ScrapedTvViewModel", "ScrapedTvViewModel:com.google.jetstream.presentation.screens.home"], "src\\main\\java\\com\\google\\jetstream\\data\\util\\AssetReader.kt": ["getJsonDataFromAsset:com.google.jetstream.data.util.AssetsReader", "AssetsReader:com.google.jetstream.data.util"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\WebDavConfigSection.kt": ["WebDavConfigSection:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\data\\database\\entities\\WebDavConfigEntity.kt": ["id:com.google.jetstream.data.database.entities.WebDavConfigEntity", "displayName:com.google.jetstream.data.database.entities.WebDavConfigEntity", "WebDavConfigEntity:com.google.jetstream.data.database.entities", "isConnected:com.google.jetstream.data.database.entities.WebDavConfigEntity", "createdAt:com.google.jetstream.data.database.entities.WebDavConfigEntity", "username:com.google.jetstream.data.database.entities.WebDavConfigEntity", "serverUrl:com.google.jetstream.data.database.entities.WebDavConfigEntity", "password:com.google.jetstream.data.database.entities.WebDavConfigEntity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\di\\DatabaseModule_ProvideRecentlyWatchedDaoFactory.java": ["create:com.google.jetstream.di.DatabaseModule_ProvideRecentlyWatchedDaoFactory", "provideRecentlyWatchedDao:com.google.jetstream.di.DatabaseModule_ProvideRecentlyWatchedDaoFactory", "DatabaseModule_ProvideRecentlyWatchedDaoFactory:com.google.jetstream.di", "get:com.google.jetstream.di.DatabaseModule_ProvideRecentlyWatchedDaoFactory", "<init>:com.google.jetstream.di.DatabaseModule_ProvideRecentlyWatchedDaoFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_google_jetstream_presentation_screens_profile_WebDavBrowserViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_google_jetstream_presentation_screens_profile_WebDavBrowserViewModel_HiltModules_KeyModule", "_com_google_jetstream_presentation_screens_profile_WebDavBrowserViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\webdav\\WebDavConfigViewModel_HiltModules.java": ["BindsModule:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules", "WebDavConfigViewModel_HiltModules:com.google.jetstream.presentation.screens.webdav", "binds:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules.BindsModule", "provide:com.google.jetstream.presentation.screens.webdav.WebDavConfigViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\google\\jetstream\\data\\models\\MovieCategoriesResponse.kt": ["name:com.google.jetstream.data.models.MovieCategoriesResponseItem", "MovieCategoriesResponseItem:com.google.jetstream.data.models", "id:com.google.jetstream.data.models.MovieCategoriesResponseItem"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\movies\\MoviesScreenViewModel_HiltModules.java": ["BindsModule:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules", "MoviesScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.movies", "provide:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules.KeyModule", "binds:com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules.BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\home\\ScrapedMoviesViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["lazyClassKeyName:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "ScrapedMoviesViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.home", "keepFieldType:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.google.jetstream.presentation.screens.home.ScrapedMoviesViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\VideoPlayerMediaTitle.kt": ["<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerMediaTitleType.AD", "DEFAULT:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerMediaTitleType", "<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerMediaTitleType.DEFAULT", "VideoPlayerMediaTitleType:com.google.jetstream.presentation.screens.videoPlayer.components", "AD:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerMediaTitleType", "VideoPlayerMediaTitle:com.google.jetstream.presentation.screens.videoPlayer.components", "<init>:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerMediaTitleType.LIVE", "LIVE:com.google.jetstream.presentation.screens.videoPlayer.components.VideoPlayerMediaTitleType"], "src\\main\\java\\com\\google\\jetstream\\data\\repositories\\MovieCategoryDataSource.kt": ["MovieCategoryDataSource:com.google.jetstream.data.repositories", "getMovieCategoryList:com.google.jetstream.data.repositories.MovieCategoryDataSource"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\PreviousButton.kt": ["PreviousButton:com.google.jetstream.presentation.screens.videoPlayer.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\favourites\\FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory.java": ["create:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory", "FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory:com.google.jetstream.presentation.screens.favourites", "provide:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\favourites\\FavouriteScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "FavouriteScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.favourites"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\MovieDataSource_Factory.java": ["<init>:com.google.jetstream.data.repositories.MovieDataSource_Factory", "newInstance:com.google.jetstream.data.repositories.MovieDataSource_Factory", "MovieDataSource_Factory:com.google.jetstream.data.repositories", "get:com.google.jetstream.data.repositories.MovieDataSource_Factory", "create:com.google.jetstream.data.repositories.MovieDataSource_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\ProfileScreen.kt": ["ProfileScreenPreview:com.google.jetstream.presentation.screens.profile", "ProfileScreen:com.google.jetstream.presentation.screens.profile"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\repositories\\TvDataSource_Factory.java": ["create:com.google.jetstream.data.repositories.TvDataSource_Factory", "TvDataSource_Factory:com.google.jetstream.data.repositories", "newInstance:com.google.jetstream.data.repositories.TvDataSource_Factory", "<init>:com.google.jetstream.data.repositories.TvDataSource_Factory", "get:com.google.jetstream.data.repositories.TvDataSource_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\videoPlayer\\components\\RepeatButton.kt": ["RepeatButton:com.google.jetstream.presentation.screens.videoPlayer.components"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\movies\\MovieReviews.kt": ["MovieReviews:com.google.jetstream.presentation.screens.movies"], "src\\main\\java\\com\\google\\jetstream\\presentation\\common\\Error.kt": ["Error:com.google.jetstream.presentation.common"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_google_jetstream_JetStreamApplication.java": ["<init>:dagger.hilt.internal.aggregatedroot.codegen._com_google_jetstream_JetStreamApplication", "_com_google_jetstream_JetStreamApplication:dagger.hilt.internal.aggregatedroot.codegen"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\profile\\AccountsSectionDialogButton.kt": ["AccountsSectionDialogButton:com.google.jetstream.presentation.screens.profile"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoriesScreenViewModel.kt": ["CategoriesScreenUiState:com.google.jetstream.presentation.screens.categories", "<init>:com.google.jetstream.presentation.screens.categories.CategoriesScreenUiState.Loading", "CategoriesScreenViewModel:com.google.jetstream.presentation.screens.categories", "uiState:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel", "Loading:com.google.jetstream.presentation.screens.categories.CategoriesScreenUiState", "categoryList:com.google.jetstream.presentation.screens.categories.CategoriesScreenUiState.Ready", "Ready:com.google.jetstream.presentation.screens.categories.CategoriesScreenUiState"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\google\\jetstream\\presentation\\screens\\categories\\CategoriesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "CategoriesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.google.jetstream.presentation.screens.categories", "keepFieldType:com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\home\\HomeScreenViewModel.kt": ["uiState:com.google.jetstream.presentation.screens.home.HomeScreeViewModel", "nowPlayingMovieList:com.google.jetstream.presentation.screens.home.HomeScreenUiState.Ready", "Loading:com.google.jetstream.presentation.screens.home.HomeScreenUiState", "featuredMovieList:com.google.jetstream.presentation.screens.home.HomeScreenUiState.Ready", "trendingMovieList:com.google.jetstream.presentation.screens.home.HomeScreenUiState.Ready", "HomeScreenUiState:com.google.jetstream.presentation.screens.home", "<init>:com.google.jetstream.presentation.screens.home.HomeScreenUiState.Loading", "Error:com.google.jetstream.presentation.screens.home.HomeScreenUiState", "moviesWithLongThumbnail:com.google.jetstream.presentation.screens.home.HomeScreenUiState.Ready", "Ready:com.google.jetstream.presentation.screens.home.HomeScreenUiState", "top10MovieList:com.google.jetstream.presentation.screens.home.HomeScreenUiState.Ready", "<init>:com.google.jetstream.presentation.screens.home.HomeScreenUiState.Error", "HomeScreeViewModel:com.google.jetstream.presentation.screens.home"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\presentation\\screens\\search\\SearchScreenViewModel_HiltModules.java": ["BindsModule:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules", "KeyModule:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules", "SearchScreenViewModel_HiltModules:com.google.jetstream.presentation.screens.search", "provide:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules.KeyModule", "binds:com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules.BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\google\\jetstream\\data\\services\\TvPlaybackService_Factory.java": ["get:com.google.jetstream.data.services.TvPlaybackService_Factory", "newInstance:com.google.jetstream.data.services.TvPlaybackService_Factory", "TvPlaybackService_Factory:com.google.jetstream.data.services", "create:com.google.jetstream.data.services.TvPlaybackService_Factory", "<init>:com.google.jetstream.data.services.TvPlaybackService_Factory"], "src\\main\\java\\com\\google\\jetstream\\presentation\\screens\\favourites\\FavouriteScreenViewModel.kt": ["Loading:com.google.jetstream.presentation.screens.favourites.FavouriteScreenUiState", "<init>:com.google.jetstream.presentation.screens.favourites.FilterCondition.Movies", "Movies:com.google.jetstream.presentation.screens.favourites.FilterCondition", "FilterCondition:com.google.jetstream.presentation.screens.favourites", "FilterList:com.google.jetstream.presentation.screens.favourites", "updateSelectedFilterList:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel", "Companion:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel", "idList:com.google.jetstream.presentation.screens.favourites.FilterCondition", "TvShows:com.google.jetstream.presentation.screens.favourites.FilterCondition", "AvailableIn4K:com.google.jetstream.presentation.screens.favourites.FilterCondition", "filterList:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel.Companion", "toIdList:com.google.jetstream.presentation.screens.favourites.FilterList", "Ready:com.google.jetstream.presentation.screens.favourites.FavouriteScreenUiState", "uiState:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel", "favouriteMovieList:com.google.jetstream.presentation.screens.favourites.FavouriteScreenUiState.Ready", "<init>:com.google.jetstream.presentation.screens.favourites.FilterCondition.TvShows", "<init>:com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel.Companion", "selectedFilterList:com.google.jetstream.presentation.screens.favourites.FavouriteScreenUiState.Ready", "<init>:com.google.jetstream.presentation.screens.favourites.FilterCondition.None", "FavouriteScreenUiState:com.google.jetstream.presentation.screens.favourites", "FavouriteScreenViewModel:com.google.jetstream.presentation.screens.favourites", "<init>:com.google.jetstream.presentation.screens.favourites.FilterCondition.AddedLastWeek", "<init>:com.google.jetstream.presentation.screens.favourites.FavouriteScreenUiState.Loading", "labelId:com.google.jetstream.presentation.screens.favourites.FilterCondition", "None:com.google.jetstream.presentation.screens.favourites.FilterCondition", "AddedLastWeek:com.google.jetstream.presentation.screens.favourites.FilterCondition", "<init>:com.google.jetstream.presentation.screens.favourites.FilterCondition.AvailableIn4K", "items:com.google.jetstream.presentation.screens.favourites.FilterList"]}