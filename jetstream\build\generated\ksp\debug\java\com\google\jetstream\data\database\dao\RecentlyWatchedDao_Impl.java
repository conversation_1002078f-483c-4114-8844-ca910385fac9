package com.google.jetstream.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.google.jetstream.data.database.entities.RecentlyWatchedEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class RecentlyWatchedDao_Impl implements RecentlyWatchedDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<RecentlyWatchedEntity> __insertionAdapterOfRecentlyWatchedEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteByMovieId;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldest;

  public RecentlyWatchedDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfRecentlyWatchedEntity = new EntityInsertionAdapter<RecentlyWatchedEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `recently_watched` (`movieId`,`movieTitle`,`backdropUri`,`posterUri`,`description`,`releaseDate`,`rating`,`type`,`lastWatchedAt`,`watchProgress`,`currentPositionMs`,`durationMs`,`episodeId`,`episodeNumber`,`seasonNumber`,`episodeTitle`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final RecentlyWatchedEntity entity) {
        statement.bindString(1, entity.getMovieId());
        statement.bindString(2, entity.getMovieTitle());
        statement.bindString(3, entity.getBackdropUri());
        statement.bindString(4, entity.getPosterUri());
        statement.bindString(5, entity.getDescription());
        if (entity.getReleaseDate() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getReleaseDate());
        }
        if (entity.getRating() == null) {
          statement.bindNull(7);
        } else {
          statement.bindDouble(7, entity.getRating());
        }
        statement.bindString(8, entity.getType());
        statement.bindLong(9, entity.getLastWatchedAt());
        if (entity.getWatchProgress() == null) {
          statement.bindNull(10);
        } else {
          statement.bindDouble(10, entity.getWatchProgress());
        }
        if (entity.getCurrentPositionMs() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getCurrentPositionMs());
        }
        if (entity.getDurationMs() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getDurationMs());
        }
        if (entity.getEpisodeId() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getEpisodeId());
        }
        if (entity.getEpisodeNumber() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getEpisodeNumber());
        }
        if (entity.getSeasonNumber() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getSeasonNumber());
        }
        if (entity.getEpisodeTitle() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getEpisodeTitle());
        }
      }
    };
    this.__preparedStmtOfDeleteByMovieId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM recently_watched WHERE movieId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM recently_watched";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldest = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM recently_watched WHERE movieId IN (SELECT movieId FROM recently_watched ORDER BY lastWatchedAt ASC LIMIT ?)";
        return _query;
      }
    };
  }

  @Override
  public Object insertOrUpdate(final RecentlyWatchedEntity item,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfRecentlyWatchedEntity.insert(item);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteByMovieId(final String movieId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteByMovieId.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, movieId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteByMovieId.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldest(final int deleteCount, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldest.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, deleteCount);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldest.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<RecentlyWatchedEntity>> getRecentlyWatched(final int limit) {
    final String _sql = "SELECT * FROM recently_watched ORDER BY lastWatchedAt DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"recently_watched"}, new Callable<List<RecentlyWatchedEntity>>() {
      @Override
      @NonNull
      public List<RecentlyWatchedEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfMovieId = CursorUtil.getColumnIndexOrThrow(_cursor, "movieId");
          final int _cursorIndexOfMovieTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "movieTitle");
          final int _cursorIndexOfBackdropUri = CursorUtil.getColumnIndexOrThrow(_cursor, "backdropUri");
          final int _cursorIndexOfPosterUri = CursorUtil.getColumnIndexOrThrow(_cursor, "posterUri");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfReleaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "releaseDate");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfLastWatchedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchedAt");
          final int _cursorIndexOfWatchProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "watchProgress");
          final int _cursorIndexOfCurrentPositionMs = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPositionMs");
          final int _cursorIndexOfDurationMs = CursorUtil.getColumnIndexOrThrow(_cursor, "durationMs");
          final int _cursorIndexOfEpisodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "episodeId");
          final int _cursorIndexOfEpisodeNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "episodeNumber");
          final int _cursorIndexOfSeasonNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonNumber");
          final int _cursorIndexOfEpisodeTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "episodeTitle");
          final List<RecentlyWatchedEntity> _result = new ArrayList<RecentlyWatchedEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final RecentlyWatchedEntity _item;
            final String _tmpMovieId;
            _tmpMovieId = _cursor.getString(_cursorIndexOfMovieId);
            final String _tmpMovieTitle;
            _tmpMovieTitle = _cursor.getString(_cursorIndexOfMovieTitle);
            final String _tmpBackdropUri;
            _tmpBackdropUri = _cursor.getString(_cursorIndexOfBackdropUri);
            final String _tmpPosterUri;
            _tmpPosterUri = _cursor.getString(_cursorIndexOfPosterUri);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpReleaseDate;
            if (_cursor.isNull(_cursorIndexOfReleaseDate)) {
              _tmpReleaseDate = null;
            } else {
              _tmpReleaseDate = _cursor.getString(_cursorIndexOfReleaseDate);
            }
            final Float _tmpRating;
            if (_cursor.isNull(_cursorIndexOfRating)) {
              _tmpRating = null;
            } else {
              _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            }
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final long _tmpLastWatchedAt;
            _tmpLastWatchedAt = _cursor.getLong(_cursorIndexOfLastWatchedAt);
            final Float _tmpWatchProgress;
            if (_cursor.isNull(_cursorIndexOfWatchProgress)) {
              _tmpWatchProgress = null;
            } else {
              _tmpWatchProgress = _cursor.getFloat(_cursorIndexOfWatchProgress);
            }
            final Long _tmpCurrentPositionMs;
            if (_cursor.isNull(_cursorIndexOfCurrentPositionMs)) {
              _tmpCurrentPositionMs = null;
            } else {
              _tmpCurrentPositionMs = _cursor.getLong(_cursorIndexOfCurrentPositionMs);
            }
            final Long _tmpDurationMs;
            if (_cursor.isNull(_cursorIndexOfDurationMs)) {
              _tmpDurationMs = null;
            } else {
              _tmpDurationMs = _cursor.getLong(_cursorIndexOfDurationMs);
            }
            final String _tmpEpisodeId;
            if (_cursor.isNull(_cursorIndexOfEpisodeId)) {
              _tmpEpisodeId = null;
            } else {
              _tmpEpisodeId = _cursor.getString(_cursorIndexOfEpisodeId);
            }
            final Integer _tmpEpisodeNumber;
            if (_cursor.isNull(_cursorIndexOfEpisodeNumber)) {
              _tmpEpisodeNumber = null;
            } else {
              _tmpEpisodeNumber = _cursor.getInt(_cursorIndexOfEpisodeNumber);
            }
            final Integer _tmpSeasonNumber;
            if (_cursor.isNull(_cursorIndexOfSeasonNumber)) {
              _tmpSeasonNumber = null;
            } else {
              _tmpSeasonNumber = _cursor.getInt(_cursorIndexOfSeasonNumber);
            }
            final String _tmpEpisodeTitle;
            if (_cursor.isNull(_cursorIndexOfEpisodeTitle)) {
              _tmpEpisodeTitle = null;
            } else {
              _tmpEpisodeTitle = _cursor.getString(_cursorIndexOfEpisodeTitle);
            }
            _item = new RecentlyWatchedEntity(_tmpMovieId,_tmpMovieTitle,_tmpBackdropUri,_tmpPosterUri,_tmpDescription,_tmpReleaseDate,_tmpRating,_tmpType,_tmpLastWatchedAt,_tmpWatchProgress,_tmpCurrentPositionMs,_tmpDurationMs,_tmpEpisodeId,_tmpEpisodeNumber,_tmpSeasonNumber,_tmpEpisodeTitle);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getByMovieId(final String movieId,
      final Continuation<? super RecentlyWatchedEntity> $completion) {
    final String _sql = "SELECT * FROM recently_watched WHERE movieId = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, movieId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<RecentlyWatchedEntity>() {
      @Override
      @Nullable
      public RecentlyWatchedEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfMovieId = CursorUtil.getColumnIndexOrThrow(_cursor, "movieId");
          final int _cursorIndexOfMovieTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "movieTitle");
          final int _cursorIndexOfBackdropUri = CursorUtil.getColumnIndexOrThrow(_cursor, "backdropUri");
          final int _cursorIndexOfPosterUri = CursorUtil.getColumnIndexOrThrow(_cursor, "posterUri");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfReleaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "releaseDate");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfLastWatchedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "lastWatchedAt");
          final int _cursorIndexOfWatchProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "watchProgress");
          final int _cursorIndexOfCurrentPositionMs = CursorUtil.getColumnIndexOrThrow(_cursor, "currentPositionMs");
          final int _cursorIndexOfDurationMs = CursorUtil.getColumnIndexOrThrow(_cursor, "durationMs");
          final int _cursorIndexOfEpisodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "episodeId");
          final int _cursorIndexOfEpisodeNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "episodeNumber");
          final int _cursorIndexOfSeasonNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonNumber");
          final int _cursorIndexOfEpisodeTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "episodeTitle");
          final RecentlyWatchedEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpMovieId;
            _tmpMovieId = _cursor.getString(_cursorIndexOfMovieId);
            final String _tmpMovieTitle;
            _tmpMovieTitle = _cursor.getString(_cursorIndexOfMovieTitle);
            final String _tmpBackdropUri;
            _tmpBackdropUri = _cursor.getString(_cursorIndexOfBackdropUri);
            final String _tmpPosterUri;
            _tmpPosterUri = _cursor.getString(_cursorIndexOfPosterUri);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpReleaseDate;
            if (_cursor.isNull(_cursorIndexOfReleaseDate)) {
              _tmpReleaseDate = null;
            } else {
              _tmpReleaseDate = _cursor.getString(_cursorIndexOfReleaseDate);
            }
            final Float _tmpRating;
            if (_cursor.isNull(_cursorIndexOfRating)) {
              _tmpRating = null;
            } else {
              _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            }
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final long _tmpLastWatchedAt;
            _tmpLastWatchedAt = _cursor.getLong(_cursorIndexOfLastWatchedAt);
            final Float _tmpWatchProgress;
            if (_cursor.isNull(_cursorIndexOfWatchProgress)) {
              _tmpWatchProgress = null;
            } else {
              _tmpWatchProgress = _cursor.getFloat(_cursorIndexOfWatchProgress);
            }
            final Long _tmpCurrentPositionMs;
            if (_cursor.isNull(_cursorIndexOfCurrentPositionMs)) {
              _tmpCurrentPositionMs = null;
            } else {
              _tmpCurrentPositionMs = _cursor.getLong(_cursorIndexOfCurrentPositionMs);
            }
            final Long _tmpDurationMs;
            if (_cursor.isNull(_cursorIndexOfDurationMs)) {
              _tmpDurationMs = null;
            } else {
              _tmpDurationMs = _cursor.getLong(_cursorIndexOfDurationMs);
            }
            final String _tmpEpisodeId;
            if (_cursor.isNull(_cursorIndexOfEpisodeId)) {
              _tmpEpisodeId = null;
            } else {
              _tmpEpisodeId = _cursor.getString(_cursorIndexOfEpisodeId);
            }
            final Integer _tmpEpisodeNumber;
            if (_cursor.isNull(_cursorIndexOfEpisodeNumber)) {
              _tmpEpisodeNumber = null;
            } else {
              _tmpEpisodeNumber = _cursor.getInt(_cursorIndexOfEpisodeNumber);
            }
            final Integer _tmpSeasonNumber;
            if (_cursor.isNull(_cursorIndexOfSeasonNumber)) {
              _tmpSeasonNumber = null;
            } else {
              _tmpSeasonNumber = _cursor.getInt(_cursorIndexOfSeasonNumber);
            }
            final String _tmpEpisodeTitle;
            if (_cursor.isNull(_cursorIndexOfEpisodeTitle)) {
              _tmpEpisodeTitle = null;
            } else {
              _tmpEpisodeTitle = _cursor.getString(_cursorIndexOfEpisodeTitle);
            }
            _result = new RecentlyWatchedEntity(_tmpMovieId,_tmpMovieTitle,_tmpBackdropUri,_tmpPosterUri,_tmpDescription,_tmpReleaseDate,_tmpRating,_tmpType,_tmpLastWatchedAt,_tmpWatchProgress,_tmpCurrentPositionMs,_tmpDurationMs,_tmpEpisodeId,_tmpEpisodeNumber,_tmpSeasonNumber,_tmpEpisodeTitle);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM recently_watched";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
