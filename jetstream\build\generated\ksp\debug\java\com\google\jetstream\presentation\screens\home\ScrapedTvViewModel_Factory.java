package com.google.jetstream.presentation.screens.home;

import com.google.jetstream.data.repositories.ScrapedTvStore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ScrapedTvViewModel_Factory implements Factory<ScrapedTvViewModel> {
  private final Provider<ScrapedTvStore> storeProvider;

  public ScrapedTvViewModel_Factory(Provider<ScrapedTvStore> storeProvider) {
    this.storeProvider = storeProvider;
  }

  @Override
  public ScrapedTvViewModel get() {
    return newInstance(storeProvider.get());
  }

  public static ScrapedTvViewModel_Factory create(
      javax.inject.Provider<ScrapedTvStore> storeProvider) {
    return new ScrapedTvViewModel_Factory(Providers.asDaggerProvider(storeProvider));
  }

  public static ScrapedTvViewModel_Factory create(Provider<ScrapedTvStore> storeProvider) {
    return new ScrapedTvViewModel_Factory(storeProvider);
  }

  public static ScrapedTvViewModel newInstance(ScrapedTvStore store) {
    return new ScrapedTvViewModel(store);
  }
}
