{"logs": [{"outputFile": "com.google.jetstream-mergeDebugResources-66:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67a3f5723098babf64b890147a256c9e\\transformed\\foundation-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "9199,9285", "endColumns": "85,84", "endOffsets": "9280,9365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\598ef20c77cd1b7c623fcd4d2eedf4e3\\transformed\\ui-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1613"}, "to": {"startLines": "9,10,11,12,13,23,24,82,83,84,85,86,87,88,89,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,916,998,1092,1191,1925,2007,8210,8299,8383,8461,8543,8616,8692,8764,8935,9012,9078", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "911,993,1087,1186,1273,2002,2091,8294,8378,8456,8538,8611,8687,8759,8829,9007,9073,9194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76081e3f0d6e0e3bdde7fa4601d62d99\\transformed\\core-1.15.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,90", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,708,8834", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "196,298,397,496,600,703,819,8930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\056f65275b6119073c278699d563b9b6\\transformed\\media3-exoplayer-1.6.0-beta01\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1278,1348,1410,1475,1539,1616,1681,1771,1856", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "1343,1405,1470,1534,1611,1676,1766,1851,1920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3ff6a694fcbdd48a9d50b8a2d9e5cf55\\transformed\\material3-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4530,4617,4704,4806,4888,4972,5073,5174,5274,5373,5461,5567,5668,5772,5892,5974,6074", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4525,4612,4699,4801,4883,4967,5068,5169,5269,5368,5456,5562,5663,5767,5887,5969,6069,6164"}, "to": {"startLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2096,2214,2330,2441,2555,2654,2749,2861,2997,3113,3249,3333,3432,3523,3620,3739,3864,3968,4095,4218,4346,4507,4628,4744,4867,4992,5084,5182,5299,5423,5520,5622,5724,5854,5993,6099,6198,6276,6372,6466,6571,6658,6745,6847,6929,7013,7114,7215,7315,7414,7502,7608,7709,7813,7933,8015,8115", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "2209,2325,2436,2550,2649,2744,2856,2992,3108,3244,3328,3427,3518,3615,3734,3859,3963,4090,4213,4341,4502,4623,4739,4862,4987,5079,5177,5294,5418,5515,5617,5719,5849,5988,6094,6193,6271,6367,6461,6566,6653,6740,6842,6924,7008,7109,7210,7310,7409,7497,7603,7704,7808,7928,8010,8110,8205"}}]}]}