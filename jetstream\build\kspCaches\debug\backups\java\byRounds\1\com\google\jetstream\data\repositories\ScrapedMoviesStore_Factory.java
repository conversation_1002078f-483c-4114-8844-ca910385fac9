package com.google.jetstream.data.repositories;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ScrapedMoviesStore_Factory implements Factory<ScrapedMoviesStore> {
  @Override
  public ScrapedMoviesStore get() {
    return newInstance();
  }

  public static ScrapedMoviesStore_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ScrapedMoviesStore newInstance() {
    return new ScrapedMoviesStore();
  }

  private static final class InstanceHolder {
    static final ScrapedMoviesStore_Factory INSTANCE = new ScrapedMoviesStore_Factory();
  }
}
