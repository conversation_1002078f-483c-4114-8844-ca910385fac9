R_DEF: Internal format may change without notice
local
color background
color border
color error
color errorContainer
color ic_launcher_background
color jetstream_background
color onBackground
color onError
color onErrorContainer
color onPrimary
color onPrimaryContainer
color onSecondary
color onSecondaryContainer
color onSurface
color onSurfaceVariant
color onTertiary
color onTertiaryContainer
color primary
color primaryContainer
color secondary
color secondaryContainer
color surface
color surfaceVariant
color tertiary
color tertiaryContainer
drawable app_banner_foreground
drawable ic_launcher_foreground
drawable ic_play_circle
font inter_black
font inter_bold
font inter_extra_bold
font inter_extra_light
font inter_light
font inter_medium
font inter_regular
font inter_semi_bold
font inter_thin
font lexend_exa_medium
mipmap app_banner
mipmap ic_launcher
mipmap ic_launcher_round
string ad
string app_name
string brand_logo_text
string budget
string cast_and_crew
string delete_account_dialog_text
string delete_account_dialog_title
string director
string favorites_added_last_week
string favorites_available_in_4k
string favorites_movies
string favorites_tv_shows
string favorites_unknown
string generic_error_msg
string language_section_listItem_icon_content_description
string live
string message_error
string message_loading
string movie_category_desc
string music
string no_keep_it
string original_language
string profile_screen_listItem_icon_content_description
string revenue
string reviews
string screenplay
string search_screen_et_placeholder
string status
string top_10_movies_title
string watch_now
string watch_trailer
string yes_delete_account
style Theme.App.Starting
style Theme.JetStream
xml backup_rules
xml data_extraction_rules
