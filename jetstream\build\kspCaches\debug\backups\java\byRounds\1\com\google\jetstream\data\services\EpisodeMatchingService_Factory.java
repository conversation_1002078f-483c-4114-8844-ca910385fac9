package com.google.jetstream.data.services;

import com.google.jetstream.data.webdav.WebDavService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EpisodeMatchingService_Factory implements Factory<EpisodeMatchingService> {
  private final Provider<WebDavService> webDavServiceProvider;

  public EpisodeMatchingService_Factory(Provider<WebDavService> webDavServiceProvider) {
    this.webDavServiceProvider = webDavServiceProvider;
  }

  @Override
  public EpisodeMatchingService get() {
    return newInstance(webDavServiceProvider.get());
  }

  public static EpisodeMatchingService_Factory create(
      javax.inject.Provider<WebDavService> webDavServiceProvider) {
    return new EpisodeMatchingService_Factory(Providers.asDaggerProvider(webDavServiceProvider));
  }

  public static EpisodeMatchingService_Factory create(
      Provider<WebDavService> webDavServiceProvider) {
    return new EpisodeMatchingService_Factory(webDavServiceProvider);
  }

  public static EpisodeMatchingService newInstance(WebDavService webDavService) {
    return new EpisodeMatchingService(webDavService);
  }
}
