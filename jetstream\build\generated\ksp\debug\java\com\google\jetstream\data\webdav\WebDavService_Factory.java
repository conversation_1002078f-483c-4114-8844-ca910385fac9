package com.google.jetstream.data.webdav;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class WebDavService_Factory implements Factory<WebDavService> {
  @Override
  public WebDavService get() {
    return newInstance();
  }

  public static WebDavService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static WebDavService newInstance() {
    return new WebDavService();
  }

  private static final class InstanceHolder {
    static final WebDavService_Factory INSTANCE = new WebDavService_Factory();
  }
}
