package com.google.jetstream.presentation.screens.home;

import com.google.jetstream.data.repositories.RecentlyWatchedRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RecentlyWatchedViewModel_Factory implements Factory<RecentlyWatchedViewModel> {
  private final Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider;

  public RecentlyWatchedViewModel_Factory(
      Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider) {
    this.recentlyWatchedRepositoryProvider = recentlyWatchedRepositoryProvider;
  }

  @Override
  public RecentlyWatchedViewModel get() {
    return newInstance(recentlyWatchedRepositoryProvider.get());
  }

  public static RecentlyWatchedViewModel_Factory create(
      javax.inject.Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider) {
    return new RecentlyWatchedViewModel_Factory(Providers.asDaggerProvider(recentlyWatchedRepositoryProvider));
  }

  public static RecentlyWatchedViewModel_Factory create(
      Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider) {
    return new RecentlyWatchedViewModel_Factory(recentlyWatchedRepositoryProvider);
  }

  public static RecentlyWatchedViewModel newInstance(
      RecentlyWatchedRepository recentlyWatchedRepository) {
    return new RecentlyWatchedViewModel(recentlyWatchedRepository);
  }
}
