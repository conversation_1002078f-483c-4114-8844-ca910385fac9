package com.google.jetstream.presentation.screens.movies;

import com.google.jetstream.data.repositories.MovieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MoviesScreenViewModel_Factory implements Factory<MoviesScreenViewModel> {
  private final Provider<MovieRepository> movieRepositoryProvider;

  public MoviesScreenViewModel_Factory(Provider<MovieRepository> movieRepositoryProvider) {
    this.movieRepositoryProvider = movieRepositoryProvider;
  }

  @Override
  public MoviesScreenViewModel get() {
    return newInstance(movieRepositoryProvider.get());
  }

  public static MoviesScreenViewModel_Factory create(
      javax.inject.Provider<MovieRepository> movieRepositoryProvider) {
    return new MoviesScreenViewModel_Factory(Providers.asDaggerProvider(movieRepositoryProvider));
  }

  public static MoviesScreenViewModel_Factory create(
      Provider<MovieRepository> movieRepositoryProvider) {
    return new MoviesScreenViewModel_Factory(movieRepositoryProvider);
  }

  public static MoviesScreenViewModel newInstance(MovieRepository movieRepository) {
    return new MoviesScreenViewModel(movieRepository);
  }
}
