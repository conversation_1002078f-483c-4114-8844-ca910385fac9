-- Merging decision tree log ---
manifest
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:18:1-61:12
INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:18:1-61:12
INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:18:1-61:12
INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:18:1-61:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\91d04d50bcf0e0905fe193dc997bd76b\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\bde9d466d7db30f2cc21a7fe71406064\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\c27db03c4863491c9a7de21f025bc191\transformed\navigation-common-2.8.8\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\de8ad5933734fe01333c8ce03dbebdb6\transformed\navigation-runtime-2.8.8\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\92fba41798b80ab0cd0b780ef1913db1\transformed\navigation-common-ktx-2.8.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\caddf65c245c04d59bd3739431526603\transformed\navigation-runtime-ktx-2.8.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\9245fadb5d18c052c6727d93abb11564\transformed\navigation-compose-2.8.8\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.54] D:\AndroidSdk\caches\8.10.2\transforms\1460be5ed8dad6ccba3fa6d940fd8106\transformed\hilt-android-2.54\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] D:\AndroidSdk\caches\8.10.2\transforms\2683060292804e831097c0bbf5535791\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\eb9ff48044364c3fad1cf2adadd08589\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\056f65275b6119073c278699d563b9b6\transformed\media3-exoplayer-1.6.0-beta01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e040d4742ba62decb7c3d44512d02849\transformed\media3-extractor-1.6.0-beta01\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\bab0cd406367c7dd3298af5d179477d1\transformed\media3-container-1.6.0-beta01\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\2cc6af314c638b8eac197112dd1c690d\transformed\media3-datasource-1.6.0-beta01\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\c0fd92d29761e3733485c79142a81ad0\transformed\media3-decoder-1.6.0-beta01\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\387fee4520f9de1d6bb28f6c9fecef71\transformed\media3-database-1.6.0-beta01\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common-ktx:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\85de92ead901de1c95bea964ff0fe33f\transformed\media3-common-ktx-1.6.0-beta01\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e3a5b2629d08bd4b39f107fce04c5b4d\transformed\media3-common-1.6.0-beta01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui-compose:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\c414fcbfecf915c8d09c2188ab90f2ee\transformed\media3-ui-compose-1.6.0-beta01\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tv:tv-material:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\c5bbaa06c548bc599cc391ed764d920b\transformed\tv-material-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\8757d1cd896ea02ac4e9c9fd8a55c3b0\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\09e865bf7cf78d051593990e7a283e1d\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] D:\AndroidSdk\caches\8.10.2\transforms\3ff6a694fcbdd48a9d50b8a2d9e5cf55\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\e73e675e037694b1886223fe97314932\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\76054984d7043da10e3eb0220974c0dd\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\4037ff5c7376ea8a5753e2314b00509a\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\83a5bfb3f93b9a9cee1d0691a8f729fc\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\2fe40175c7063fc1abbcd279124bde58\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\b35bcc25e408628f39963a658e34e98f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ff96918dc3fd9dcb783e0e9a5b45154\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\dbca547b24d6835a1383eb77d5bcf419\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\206c82f3be136f083be7fcbc2b65c7f8\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\abe3f4fcb041e215697a965011adc6db\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\f3f59f28af9687780c159282a361e81b\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\3eeaa128802b60eee8a681d54aa07d79\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\67a3f5723098babf64b890147a256c9e\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\3417ef9483a1258105a899c7a81542ce\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\08e6d96345edfebc535d44bf9c21a277\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\c05fb5c4dfbbdbd4e7569e3d552cca2b\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\150c03a8d21ab2a4eb785d5874481ac1\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\AndroidSdk\caches\8.10.2\transforms\8cc7ab9342e28d3a7a61e29019635d2d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\223548ace4416c5f4316bc856de16d89\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\AndroidSdk\caches\8.10.2\transforms\f8706489a50775357228e3c039fb56a1\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\8339e6ffe259b5f4644d79bcc96d7439\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidSdk\caches\8.10.2\transforms\465e00b7b2bddc7e5ce90d29d0e5dfd8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidSdk\caches\8.10.2\transforms\f2426ae6ca8a686ecbf8cac0a3f545d5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\AndroidSdk\caches\8.10.2\transforms\decba1422ae440d4f499e9e1e6acd1c5\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\AndroidSdk\caches\8.10.2\transforms\5da86a88389be1e494453e72f8f377b2\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f13610f349f9842856938f045cd4d4c4\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\39e41bc2b8c38bf53eb9e9341f5ff7cd\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\69928a583909bcd7ab5ab82f7b978da3\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\2b114b0bd796e9060d848c372b0f76ef\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f9deb594537463bd032194fa21f2274e\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\eef42f5ef742ab64fa8117b677e8f83e\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\94313cde90364a617dd5773a9d9aa7ce\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\7c729525d4eda050d9bb3212162dea2f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\d29bd199c2a7b7d9cf8df8b78acba8de\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\75bb1a0789e6dc3df928c2dc839a9cb0\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f1dc5d846b9606f1415d5d864c60d7fa\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] D:\AndroidSdk\caches\8.10.2\transforms\076a24407656cb690bbd02597a4dfa2b\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\8b95954bae16adf9aea8783799863688\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\c05ce9de39d52418da0ed7019a26471c\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\598ef20c77cd1b7c623fcd4d2eedf4e3\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\05f0bd955812ef05a499257f69e9e071\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\c9841f8b8bf9f8ad2044f143abe32946\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\1deee87c461d53e27c7ec2397b1a8929\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\9dcded2536f7f430ebb3b2d9f4ece892\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] D:\AndroidSdk\caches\8.10.2\transforms\1f4a5aa1c9ee769e0f52ab0d833391f6\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\7ed97b31fc943f7e4c271931a420e3d0\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\AndroidSdk\caches\8.10.2\transforms\fd751ed6a00cf5765574738bd4492ea8\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\AndroidSdk\caches\8.10.2\transforms\db9b6adbc3ca15e36f402e963dd9161f\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\9d5b162717bed6dc35c1a21e4bad4638\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidSdk\caches\8.10.2\transforms\fbba9b8c4cd7d82a6ac389ab86729327\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\f23aa9b225129dfd90dbb7d559a3c168\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\50bbe34670789c4d7afe9b03eacb33ba\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\f0abe17b43d40c8a1d590df10429b291\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\120103cfbaa55ba50022adac62cb30cb\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\81f81a72a787792e71d73b2a2e3f7c9f\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\AndroidSdk\caches\8.10.2\transforms\23f28063449cc68a39d3d2f9d9621ffc\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\158844272f226902ca55f451b53ac952\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\cedd32b86dc8d6e4ba9ff279133a7a14\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.thegrizzlylabs:sardine-android:0.9] D:\AndroidSdk\caches\8.10.2\transforms\b053417f0a8db06924b64b48c017f318\transformed\sardine-android-0.9\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:dagger-lint-aar:2.54] D:\AndroidSdk\caches\8.10.2\transforms\baab3f34e9aee392e8ab28a5e6193fcc\transformed\dagger-lint-aar-2.54\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:19:5-51
	android:versionCode
		INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:18:11-69
uses-feature#android.software.leanback
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:21:5-23:36
	android:required
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:23:9-33
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:22:9-49
uses-feature#android.hardware.touchscreen
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:27:9-33
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:26:9-52
uses-permission#android.permission.INTERNET
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:29:5-67
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:29:22-64
application
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:31:5-59:19
INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:31:5-59:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\9d5b162717bed6dc35c1a21e4bad4638\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\9d5b162717bed6dc35c1a21e4bad4638\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\f0abe17b43d40c8a1d590df10429b291\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\f0abe17b43d40c8a1d590df10429b291\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:39:9-35
	android:label
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:38:9-41
	android:fullBackupContent
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:36:9-54
	tools:targetApi
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:42:9-29
	android:icon
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:37:9-43
	android:allowBackup
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:33:9-35
	android:banner
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:34:9-44
	android:theme
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:40:9-50
	android:dataExtractionRules
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:35:9-65
	android:usesCleartextTraffic
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:41:9-44
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:32:9-45
profileable
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:45:9-47:36
	android:shell
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:46:13-33
	tools:targetApi
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:47:13-33
activity#com.google.jetstream.MainActivity
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:49:9-58:20
	android:exported
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:51:13-36
	android:theme
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:52:13-54
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:50:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:53:13-57:29
action#android.intent.action.MAIN
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:54:17-69
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:54:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:55:17-77
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:55:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:56:17-86
	android:name
		ADDED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml:56:27-83
uses-sdk
INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\91d04d50bcf0e0905fe193dc997bd76b\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\91d04d50bcf0e0905fe193dc997bd76b\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\bde9d466d7db30f2cc21a7fe71406064\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\bde9d466d7db30f2cc21a7fe71406064\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\c27db03c4863491c9a7de21f025bc191\transformed\navigation-common-2.8.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\c27db03c4863491c9a7de21f025bc191\transformed\navigation-common-2.8.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\de8ad5933734fe01333c8ce03dbebdb6\transformed\navigation-runtime-2.8.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\de8ad5933734fe01333c8ce03dbebdb6\transformed\navigation-runtime-2.8.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\92fba41798b80ab0cd0b780ef1913db1\transformed\navigation-common-ktx-2.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\92fba41798b80ab0cd0b780ef1913db1\transformed\navigation-common-ktx-2.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\caddf65c245c04d59bd3739431526603\transformed\navigation-runtime-ktx-2.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\caddf65c245c04d59bd3739431526603\transformed\navigation-runtime-ktx-2.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\9245fadb5d18c052c6727d93abb11564\transformed\navigation-compose-2.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.8] D:\AndroidSdk\caches\8.10.2\transforms\9245fadb5d18c052c6727d93abb11564\transformed\navigation-compose-2.8.8\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.54] D:\AndroidSdk\caches\8.10.2\transforms\1460be5ed8dad6ccba3fa6d940fd8106\transformed\hilt-android-2.54\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.54] D:\AndroidSdk\caches\8.10.2\transforms\1460be5ed8dad6ccba3fa6d940fd8106\transformed\hilt-android-2.54\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] D:\AndroidSdk\caches\8.10.2\transforms\2683060292804e831097c0bbf5535791\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] D:\AndroidSdk\caches\8.10.2\transforms\2683060292804e831097c0bbf5535791\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\eb9ff48044364c3fad1cf2adadd08589\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\eb9ff48044364c3fad1cf2adadd08589\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\056f65275b6119073c278699d563b9b6\transformed\media3-exoplayer-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\056f65275b6119073c278699d563b9b6\transformed\media3-exoplayer-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e040d4742ba62decb7c3d44512d02849\transformed\media3-extractor-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e040d4742ba62decb7c3d44512d02849\transformed\media3-extractor-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\bab0cd406367c7dd3298af5d179477d1\transformed\media3-container-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\bab0cd406367c7dd3298af5d179477d1\transformed\media3-container-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\2cc6af314c638b8eac197112dd1c690d\transformed\media3-datasource-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\2cc6af314c638b8eac197112dd1c690d\transformed\media3-datasource-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\c0fd92d29761e3733485c79142a81ad0\transformed\media3-decoder-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\c0fd92d29761e3733485c79142a81ad0\transformed\media3-decoder-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\387fee4520f9de1d6bb28f6c9fecef71\transformed\media3-database-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\387fee4520f9de1d6bb28f6c9fecef71\transformed\media3-database-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common-ktx:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\85de92ead901de1c95bea964ff0fe33f\transformed\media3-common-ktx-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common-ktx:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\85de92ead901de1c95bea964ff0fe33f\transformed\media3-common-ktx-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e3a5b2629d08bd4b39f107fce04c5b4d\transformed\media3-common-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e3a5b2629d08bd4b39f107fce04c5b4d\transformed\media3-common-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui-compose:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\c414fcbfecf915c8d09c2188ab90f2ee\transformed\media3-ui-compose-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui-compose:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\c414fcbfecf915c8d09c2188ab90f2ee\transformed\media3-ui-compose-1.6.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.tv:tv-material:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\c5bbaa06c548bc599cc391ed764d920b\transformed\tv-material-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\c5bbaa06c548bc599cc391ed764d920b\transformed\tv-material-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\8757d1cd896ea02ac4e9c9fd8a55c3b0\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\8757d1cd896ea02ac4e9c9fd8a55c3b0\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\09e865bf7cf78d051593990e7a283e1d\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\09e865bf7cf78d051593990e7a283e1d\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] D:\AndroidSdk\caches\8.10.2\transforms\3ff6a694fcbdd48a9d50b8a2d9e5cf55\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] D:\AndroidSdk\caches\8.10.2\transforms\3ff6a694fcbdd48a9d50b8a2d9e5cf55\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\e73e675e037694b1886223fe97314932\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\e73e675e037694b1886223fe97314932\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\76054984d7043da10e3eb0220974c0dd\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\76054984d7043da10e3eb0220974c0dd\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\4037ff5c7376ea8a5753e2314b00509a\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\4037ff5c7376ea8a5753e2314b00509a\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\83a5bfb3f93b9a9cee1d0691a8f729fc\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\83a5bfb3f93b9a9cee1d0691a8f729fc\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\2fe40175c7063fc1abbcd279124bde58\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\2fe40175c7063fc1abbcd279124bde58\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\b35bcc25e408628f39963a658e34e98f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\b35bcc25e408628f39963a658e34e98f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ff96918dc3fd9dcb783e0e9a5b45154\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ff96918dc3fd9dcb783e0e9a5b45154\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\dbca547b24d6835a1383eb77d5bcf419\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\dbca547b24d6835a1383eb77d5bcf419\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\206c82f3be136f083be7fcbc2b65c7f8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\206c82f3be136f083be7fcbc2b65c7f8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\abe3f4fcb041e215697a965011adc6db\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\abe3f4fcb041e215697a965011adc6db\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\f3f59f28af9687780c159282a361e81b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\f3f59f28af9687780c159282a361e81b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\3eeaa128802b60eee8a681d54aa07d79\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\3eeaa128802b60eee8a681d54aa07d79\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\67a3f5723098babf64b890147a256c9e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\67a3f5723098babf64b890147a256c9e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\3417ef9483a1258105a899c7a81542ce\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\3417ef9483a1258105a899c7a81542ce\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\08e6d96345edfebc535d44bf9c21a277\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\08e6d96345edfebc535d44bf9c21a277\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\c05fb5c4dfbbdbd4e7569e3d552cca2b\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\c05fb5c4dfbbdbd4e7569e3d552cca2b\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\150c03a8d21ab2a4eb785d5874481ac1\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] D:\AndroidSdk\caches\8.10.2\transforms\150c03a8d21ab2a4eb785d5874481ac1\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\AndroidSdk\caches\8.10.2\transforms\8cc7ab9342e28d3a7a61e29019635d2d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\AndroidSdk\caches\8.10.2\transforms\8cc7ab9342e28d3a7a61e29019635d2d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\223548ace4416c5f4316bc856de16d89\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\223548ace4416c5f4316bc856de16d89\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\AndroidSdk\caches\8.10.2\transforms\f8706489a50775357228e3c039fb56a1\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\AndroidSdk\caches\8.10.2\transforms\f8706489a50775357228e3c039fb56a1\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\8339e6ffe259b5f4644d79bcc96d7439\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\8339e6ffe259b5f4644d79bcc96d7439\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidSdk\caches\8.10.2\transforms\465e00b7b2bddc7e5ce90d29d0e5dfd8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidSdk\caches\8.10.2\transforms\465e00b7b2bddc7e5ce90d29d0e5dfd8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidSdk\caches\8.10.2\transforms\f2426ae6ca8a686ecbf8cac0a3f545d5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidSdk\caches\8.10.2\transforms\f2426ae6ca8a686ecbf8cac0a3f545d5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\AndroidSdk\caches\8.10.2\transforms\decba1422ae440d4f499e9e1e6acd1c5\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\AndroidSdk\caches\8.10.2\transforms\decba1422ae440d4f499e9e1e6acd1c5\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\AndroidSdk\caches\8.10.2\transforms\5da86a88389be1e494453e72f8f377b2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\AndroidSdk\caches\8.10.2\transforms\5da86a88389be1e494453e72f8f377b2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f13610f349f9842856938f045cd4d4c4\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f13610f349f9842856938f045cd4d4c4\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\39e41bc2b8c38bf53eb9e9341f5ff7cd\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\39e41bc2b8c38bf53eb9e9341f5ff7cd\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\69928a583909bcd7ab5ab82f7b978da3\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\69928a583909bcd7ab5ab82f7b978da3\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\2b114b0bd796e9060d848c372b0f76ef\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\2b114b0bd796e9060d848c372b0f76ef\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f9deb594537463bd032194fa21f2274e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f9deb594537463bd032194fa21f2274e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\eef42f5ef742ab64fa8117b677e8f83e\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\eef42f5ef742ab64fa8117b677e8f83e\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\94313cde90364a617dd5773a9d9aa7ce\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\94313cde90364a617dd5773a9d9aa7ce\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\7c729525d4eda050d9bb3212162dea2f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\7c729525d4eda050d9bb3212162dea2f\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\d29bd199c2a7b7d9cf8df8b78acba8de\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\d29bd199c2a7b7d9cf8df8b78acba8de\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\75bb1a0789e6dc3df928c2dc839a9cb0\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\75bb1a0789e6dc3df928c2dc839a9cb0\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f1dc5d846b9606f1415d5d864c60d7fa\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\f1dc5d846b9606f1415d5d864c60d7fa\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] D:\AndroidSdk\caches\8.10.2\transforms\076a24407656cb690bbd02597a4dfa2b\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] D:\AndroidSdk\caches\8.10.2\transforms\076a24407656cb690bbd02597a4dfa2b\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\8b95954bae16adf9aea8783799863688\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\8b95954bae16adf9aea8783799863688\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\c05ce9de39d52418da0ed7019a26471c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\c05ce9de39d52418da0ed7019a26471c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\598ef20c77cd1b7c623fcd4d2eedf4e3\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\598ef20c77cd1b7c623fcd4d2eedf4e3\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\05f0bd955812ef05a499257f69e9e071\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\05f0bd955812ef05a499257f69e9e071\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\c9841f8b8bf9f8ad2044f143abe32946\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\AndroidSdk\caches\8.10.2\transforms\c9841f8b8bf9f8ad2044f143abe32946\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\1deee87c461d53e27c7ec2397b1a8929\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\1deee87c461d53e27c7ec2397b1a8929\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\9dcded2536f7f430ebb3b2d9f4ece892\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\9dcded2536f7f430ebb3b2d9f4ece892\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] D:\AndroidSdk\caches\8.10.2\transforms\1f4a5aa1c9ee769e0f52ab0d833391f6\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] D:\AndroidSdk\caches\8.10.2\transforms\1f4a5aa1c9ee769e0f52ab0d833391f6\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\7ed97b31fc943f7e4c271931a420e3d0\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\7ed97b31fc943f7e4c271931a420e3d0\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\AndroidSdk\caches\8.10.2\transforms\fd751ed6a00cf5765574738bd4492ea8\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\AndroidSdk\caches\8.10.2\transforms\fd751ed6a00cf5765574738bd4492ea8\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\AndroidSdk\caches\8.10.2\transforms\db9b6adbc3ca15e36f402e963dd9161f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\AndroidSdk\caches\8.10.2\transforms\db9b6adbc3ca15e36f402e963dd9161f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\9d5b162717bed6dc35c1a21e4bad4638\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\9d5b162717bed6dc35c1a21e4bad4638\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidSdk\caches\8.10.2\transforms\fbba9b8c4cd7d82a6ac389ab86729327\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidSdk\caches\8.10.2\transforms\fbba9b8c4cd7d82a6ac389ab86729327\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\f23aa9b225129dfd90dbb7d559a3c168\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\f23aa9b225129dfd90dbb7d559a3c168\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\50bbe34670789c4d7afe9b03eacb33ba\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidSdk\caches\8.10.2\transforms\50bbe34670789c4d7afe9b03eacb33ba\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\f0abe17b43d40c8a1d590df10429b291\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\f0abe17b43d40c8a1d590df10429b291\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\120103cfbaa55ba50022adac62cb30cb\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\120103cfbaa55ba50022adac62cb30cb\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\81f81a72a787792e71d73b2a2e3f7c9f\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\81f81a72a787792e71d73b2a2e3f7c9f\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\AndroidSdk\caches\8.10.2\transforms\23f28063449cc68a39d3d2f9d9621ffc\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\AndroidSdk\caches\8.10.2\transforms\23f28063449cc68a39d3d2f9d9621ffc\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\158844272f226902ca55f451b53ac952\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\158844272f226902ca55f451b53ac952\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\cedd32b86dc8d6e4ba9ff279133a7a14\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidSdk\caches\8.10.2\transforms\cedd32b86dc8d6e4ba9ff279133a7a14\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.thegrizzlylabs:sardine-android:0.9] D:\AndroidSdk\caches\8.10.2\transforms\b053417f0a8db06924b64b48c017f318\transformed\sardine-android-0.9\AndroidManifest.xml:5:5-44
MERGED from [com.github.thegrizzlylabs:sardine-android:0.9] D:\AndroidSdk\caches\8.10.2\transforms\b053417f0a8db06924b64b48c017f318\transformed\sardine-android-0.9\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.54] D:\AndroidSdk\caches\8.10.2\transforms\baab3f34e9aee392e8ab28a5e6193fcc\transformed\dagger-lint-aar-2.54\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.54] D:\AndroidSdk\caches\8.10.2\transforms\baab3f34e9aee392e8ab28a5e6193fcc\transformed\dagger-lint-aar-2.54\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\jetstrem-tvpayer\jetstream\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-exoplayer:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\056f65275b6119073c278699d563b9b6\transformed\media3-exoplayer-1.6.0-beta01\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e3a5b2629d08bd4b39f107fce04c5b4d\transformed\media3-common-1.6.0-beta01\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\e3a5b2629d08bd4b39f107fce04c5b4d\transformed\media3-common-1.6.0-beta01\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.media3:media3-exoplayer:1.6.0-beta01] D:\AndroidSdk\caches\8.10.2\transforms\056f65275b6119073c278699d563b9b6\transformed\media3-exoplayer-1.6.0-beta01\AndroidManifest.xml:22:22-76
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] D:\AndroidSdk\caches\8.10.2\transforms\6ec09ba13f1151bacaf5cef7ca866e55\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\9d5b162717bed6dc35c1a21e4bad4638\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidSdk\caches\8.10.2\transforms\9d5b162717bed6dc35c1a21e4bad4638\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidSdk\caches\8.10.2\transforms\6d2e0a0dc350684683fc75478e3224fa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.google.jetstream.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.google.jetstream.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] D:\AndroidSdk\caches\8.10.2\transforms\76081e3f0d6e0e3bdde7fa4601d62d99\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] D:\AndroidSdk\caches\8.10.2\transforms\c46181bab736f9e5002121ea28bd3c8f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] D:\AndroidSdk\caches\8.10.2\transforms\4091c30ffcdf095e6dc5ff49fa4d450a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] D:\AndroidSdk\caches\8.10.2\transforms\f02789085a826e548109e4d3072d819c\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
