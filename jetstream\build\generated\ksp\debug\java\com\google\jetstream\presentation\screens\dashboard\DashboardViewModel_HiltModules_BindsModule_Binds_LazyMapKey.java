package com.google.jetstream.presentation.screens.dashboard;

import dagger.internal.DaggerGenerated;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import javax.annotation.processing.Generated;

@IdentifierNameString
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DashboardViewModel_HiltModules_BindsModule_Binds_LazyMapKey {
  @KeepFieldType
  static DashboardViewModel keepFieldType;

  public static String lazyClassKeyName = "com.google.jetstream.presentation.screens.dashboard.DashboardViewModel";
}
