package com.google.jetstream.presentation.screens.webdav;

import com.google.jetstream.data.repositories.WebDavRepository;
import com.google.jetstream.data.webdav.WebDavService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class WebDavConfigViewModel_Factory implements Factory<WebDavConfigViewModel> {
  private final Provider<WebDavService> webDavServiceProvider;

  private final Provider<WebDavRepository> webDavRepositoryProvider;

  public WebDavConfigViewModel_Factory(Provider<WebDavService> webDavServiceProvider,
      Provider<WebDavRepository> webDavRepositoryProvider) {
    this.webDavServiceProvider = webDavServiceProvider;
    this.webDavRepositoryProvider = webDavRepositoryProvider;
  }

  @Override
  public WebDavConfigViewModel get() {
    return newInstance(webDavServiceProvider.get(), webDavRepositoryProvider.get());
  }

  public static WebDavConfigViewModel_Factory create(
      javax.inject.Provider<WebDavService> webDavServiceProvider,
      javax.inject.Provider<WebDavRepository> webDavRepositoryProvider) {
    return new WebDavConfigViewModel_Factory(Providers.asDaggerProvider(webDavServiceProvider), Providers.asDaggerProvider(webDavRepositoryProvider));
  }

  public static WebDavConfigViewModel_Factory create(Provider<WebDavService> webDavServiceProvider,
      Provider<WebDavRepository> webDavRepositoryProvider) {
    return new WebDavConfigViewModel_Factory(webDavServiceProvider, webDavRepositoryProvider);
  }

  public static WebDavConfigViewModel newInstance(WebDavService webDavService,
      WebDavRepository webDavRepository) {
    return new WebDavConfigViewModel(webDavService, webDavRepository);
  }
}
