package com.google.jetstream.data.repositories;

import com.google.jetstream.data.util.AssetsReader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class TvDataSource_Factory implements Factory<TvDataSource> {
  private final Provider<AssetsReader> assetsReaderProvider;

  public TvDataSource_Factory(Provider<AssetsReader> assetsReaderProvider) {
    this.assetsReaderProvider = assetsReaderProvider;
  }

  @Override
  public TvDataSource get() {
    return newInstance(assetsReaderProvider.get());
  }

  public static TvDataSource_Factory create(
      javax.inject.Provider<AssetsReader> assetsReaderProvider) {
    return new TvDataSource_Factory(Providers.asDaggerProvider(assetsReaderProvider));
  }

  public static TvDataSource_Factory create(Provider<AssetsReader> assetsReaderProvider) {
    return new TvDataSource_Factory(assetsReaderProvider);
  }

  public static TvDataSource newInstance(AssetsReader assetsReader) {
    return new TvDataSource(assetsReader);
  }
}
