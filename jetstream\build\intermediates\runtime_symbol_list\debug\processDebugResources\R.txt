int anim fragment_fast_out_extra_slow_in 0x7f010000
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int attr action 0x7f030000
int attr alpha 0x7f030001
int attr argType 0x7f030002
int attr data 0x7f030003
int attr dataPattern 0x7f030004
int attr destination 0x7f030005
int attr enterAnim 0x7f030006
int attr exitAnim 0x7f030007
int attr font 0x7f030008
int attr fontProviderAuthority 0x7f030009
int attr fontProviderCerts 0x7f03000a
int attr fontProviderFallbackQuery 0x7f03000b
int attr fontProviderFetchStrategy 0x7f03000c
int attr fontProviderFetchTimeout 0x7f03000d
int attr fontProviderPackage 0x7f03000e
int attr fontProviderQuery 0x7f03000f
int attr fontProviderSystemFontFamily 0x7f030010
int attr fontStyle 0x7f030011
int attr fontVariationSettings 0x7f030012
int attr fontWeight 0x7f030013
int attr graph 0x7f030014
int attr lStar 0x7f030015
int attr launchSingleTop 0x7f030016
int attr mimeType 0x7f030017
int attr navGraph 0x7f030018
int attr nestedScrollViewStyle 0x7f030019
int attr nullable 0x7f03001a
int attr popEnterAnim 0x7f03001b
int attr popExitAnim 0x7f03001c
int attr popUpTo 0x7f03001d
int attr popUpToInclusive 0x7f03001e
int attr popUpToSaveState 0x7f03001f
int attr postSplashScreenTheme 0x7f030020
int attr queryPatterns 0x7f030021
int attr restoreState 0x7f030022
int attr route 0x7f030023
int attr shortcutMatchRequired 0x7f030024
int attr splashScreenIconSize 0x7f030025
int attr startDestination 0x7f030026
int attr targetPackage 0x7f030027
int attr ttcIndex 0x7f030028
int attr uri 0x7f030029
int attr windowSplashScreenAnimatedIcon 0x7f03002a
int attr windowSplashScreenAnimationDuration 0x7f03002b
int attr windowSplashScreenBackground 0x7f03002c
int attr windowSplashScreenIconBackgroundColor 0x7f03002d
int color androidx_core_ripple_material_light 0x7f040000
int color androidx_core_secondary_text_default_material_light 0x7f040001
int color background 0x7f040002
int color border 0x7f040003
int color call_notification_answer_color 0x7f040004
int color call_notification_decline_color 0x7f040005
int color error 0x7f040006
int color errorContainer 0x7f040007
int color ic_launcher_background 0x7f040008
int color jetstream_background 0x7f040009
int color notification_action_color_filter 0x7f04000a
int color notification_icon_bg_color 0x7f04000b
int color onBackground 0x7f04000c
int color onError 0x7f04000d
int color onErrorContainer 0x7f04000e
int color onPrimary 0x7f04000f
int color onPrimaryContainer 0x7f040010
int color onSecondary 0x7f040011
int color onSecondaryContainer 0x7f040012
int color onSurface 0x7f040013
int color onSurfaceVariant 0x7f040014
int color onTertiary 0x7f040015
int color onTertiaryContainer 0x7f040016
int color primary 0x7f040017
int color primaryContainer 0x7f040018
int color secondary 0x7f040019
int color secondaryContainer 0x7f04001a
int color surface 0x7f04001b
int color surfaceVariant 0x7f04001c
int color tertiary 0x7f04001d
int color tertiaryContainer 0x7f04001e
int color vector_tint_color 0x7f04001f
int color vector_tint_theme_color 0x7f040020
int dimen compat_button_inset_horizontal_material 0x7f050000
int dimen compat_button_inset_vertical_material 0x7f050001
int dimen compat_button_padding_horizontal_material 0x7f050002
int dimen compat_button_padding_vertical_material 0x7f050003
int dimen compat_control_corner_material 0x7f050004
int dimen compat_notification_large_icon_max_height 0x7f050005
int dimen compat_notification_large_icon_max_width 0x7f050006
int dimen notification_action_icon_size 0x7f050007
int dimen notification_action_text_size 0x7f050008
int dimen notification_big_circle_margin 0x7f050009
int dimen notification_content_margin_start 0x7f05000a
int dimen notification_large_icon_height 0x7f05000b
int dimen notification_large_icon_width 0x7f05000c
int dimen notification_main_column_padding_top 0x7f05000d
int dimen notification_media_narrow_margin 0x7f05000e
int dimen notification_right_icon_size 0x7f05000f
int dimen notification_right_side_padding_top 0x7f050010
int dimen notification_small_icon_background_padding 0x7f050011
int dimen notification_small_icon_size_as_large 0x7f050012
int dimen notification_subtext_size 0x7f050013
int dimen notification_top_pad 0x7f050014
int dimen notification_top_pad_large_text 0x7f050015
int dimen splashscreen_icon_mask_size_no_background 0x7f050016
int dimen splashscreen_icon_mask_size_with_background 0x7f050017
int dimen splashscreen_icon_mask_stroke_no_background 0x7f050018
int dimen splashscreen_icon_mask_stroke_with_background 0x7f050019
int dimen splashscreen_icon_size 0x7f05001a
int dimen splashscreen_icon_size_no_background 0x7f05001b
int dimen splashscreen_icon_size_with_background 0x7f05001c
int drawable abc_vector_test 0x7f060000
int drawable app_banner_foreground 0x7f060001
int drawable compat_splash_screen 0x7f060002
int drawable compat_splash_screen_no_icon_background 0x7f060003
int drawable ic_call_answer 0x7f060004
int drawable ic_call_answer_low 0x7f060005
int drawable ic_call_answer_video 0x7f060006
int drawable ic_call_answer_video_low 0x7f060007
int drawable ic_call_decline 0x7f060008
int drawable ic_call_decline_low 0x7f060009
int drawable ic_launcher_foreground 0x7f06000a
int drawable ic_play_circle 0x7f06000b
int drawable icon_background 0x7f06000c
int drawable notification_action_background 0x7f06000d
int drawable notification_bg 0x7f06000e
int drawable notification_bg_low 0x7f06000f
int drawable notification_bg_low_normal 0x7f060010
int drawable notification_bg_low_pressed 0x7f060011
int drawable notification_bg_normal 0x7f060012
int drawable notification_bg_normal_pressed 0x7f060013
int drawable notification_icon_background 0x7f060014
int drawable notification_oversize_large_icon_bg 0x7f060015
int drawable notification_template_icon_bg 0x7f060016
int drawable notification_template_icon_low_bg 0x7f060017
int drawable notification_tile_bg 0x7f060018
int drawable notify_panel_notification_icon_bg 0x7f060019
int font inter_black 0x7f070000
int font inter_bold 0x7f070001
int font inter_extra_bold 0x7f070002
int font inter_extra_light 0x7f070003
int font inter_light 0x7f070004
int font inter_medium 0x7f070005
int font inter_regular 0x7f070006
int font inter_semi_bold 0x7f070007
int font inter_thin 0x7f070008
int font lexend_exa_medium 0x7f070009
int id accessibility_action_clickable_span 0x7f080000
int id accessibility_custom_action_0 0x7f080001
int id accessibility_custom_action_1 0x7f080002
int id accessibility_custom_action_10 0x7f080003
int id accessibility_custom_action_11 0x7f080004
int id accessibility_custom_action_12 0x7f080005
int id accessibility_custom_action_13 0x7f080006
int id accessibility_custom_action_14 0x7f080007
int id accessibility_custom_action_15 0x7f080008
int id accessibility_custom_action_16 0x7f080009
int id accessibility_custom_action_17 0x7f08000a
int id accessibility_custom_action_18 0x7f08000b
int id accessibility_custom_action_19 0x7f08000c
int id accessibility_custom_action_2 0x7f08000d
int id accessibility_custom_action_20 0x7f08000e
int id accessibility_custom_action_21 0x7f08000f
int id accessibility_custom_action_22 0x7f080010
int id accessibility_custom_action_23 0x7f080011
int id accessibility_custom_action_24 0x7f080012
int id accessibility_custom_action_25 0x7f080013
int id accessibility_custom_action_26 0x7f080014
int id accessibility_custom_action_27 0x7f080015
int id accessibility_custom_action_28 0x7f080016
int id accessibility_custom_action_29 0x7f080017
int id accessibility_custom_action_3 0x7f080018
int id accessibility_custom_action_30 0x7f080019
int id accessibility_custom_action_31 0x7f08001a
int id accessibility_custom_action_4 0x7f08001b
int id accessibility_custom_action_5 0x7f08001c
int id accessibility_custom_action_6 0x7f08001d
int id accessibility_custom_action_7 0x7f08001e
int id accessibility_custom_action_8 0x7f08001f
int id accessibility_custom_action_9 0x7f080020
int id action_container 0x7f080021
int id action_divider 0x7f080022
int id action_image 0x7f080023
int id action_text 0x7f080024
int id actions 0x7f080025
int id androidx_compose_ui_view_composition_context 0x7f080026
int id async 0x7f080027
int id blocking 0x7f080028
int id chronometer 0x7f080029
int id coil_request_manager 0x7f08002a
int id compose_view_saveable_id_tag 0x7f08002b
int id consume_window_insets_tag 0x7f08002c
int id dialog_button 0x7f08002d
int id edit_text_id 0x7f08002e
int id forever 0x7f08002f
int id fragment_container_view_tag 0x7f080030
int id hide_graphics_layer_in_inspector_tag 0x7f080031
int id hide_ime_id 0x7f080032
int id hide_in_inspector_tag 0x7f080033
int id icon 0x7f080034
int id icon_group 0x7f080035
int id info 0x7f080036
int id inspection_slot_table_set 0x7f080037
int id is_pooling_container_tag 0x7f080038
int id italic 0x7f080039
int id line1 0x7f08003a
int id line3 0x7f08003b
int id nav_controller_view_tag 0x7f08003c
int id normal 0x7f08003d
int id notification_background 0x7f08003e
int id notification_main_column 0x7f08003f
int id notification_main_column_container 0x7f080040
int id pooling_container_listener_holder_tag 0x7f080041
int id report_drawn 0x7f080042
int id right_icon 0x7f080043
int id right_side 0x7f080044
int id special_effects_controller_view_tag 0x7f080045
int id splashscreen_icon_view 0x7f080046
int id tag_accessibility_actions 0x7f080047
int id tag_accessibility_clickable_spans 0x7f080048
int id tag_accessibility_heading 0x7f080049
int id tag_accessibility_pane_title 0x7f08004a
int id tag_on_apply_window_listener 0x7f08004b
int id tag_on_receive_content_listener 0x7f08004c
int id tag_on_receive_content_mime_types 0x7f08004d
int id tag_screen_reader_focusable 0x7f08004e
int id tag_state_description 0x7f08004f
int id tag_transition_group 0x7f080050
int id tag_unhandled_key_event_manager 0x7f080051
int id tag_unhandled_key_listeners 0x7f080052
int id tag_window_insets_animation_callback 0x7f080053
int id text 0x7f080054
int id text2 0x7f080055
int id time 0x7f080056
int id title 0x7f080057
int id view_tree_disjoint_parent 0x7f080058
int id view_tree_lifecycle_owner 0x7f080059
int id view_tree_on_back_pressed_dispatcher_owner 0x7f08005a
int id view_tree_saved_state_registry_owner 0x7f08005b
int id view_tree_view_model_store_owner 0x7f08005c
int id visible_removing_fragment_view_tag 0x7f08005d
int id wrapped_composition_tag 0x7f08005e
int integer default_icon_animation_duration 0x7f090000
int integer m3c_window_layout_in_display_cutout_mode 0x7f090001
int integer status_bar_notification_info_maxnum 0x7f090002
int layout custom_dialog 0x7f0a0000
int layout ime_base_split_test_activity 0x7f0a0001
int layout ime_secondary_split_test_activity 0x7f0a0002
int layout notification_action 0x7f0a0003
int layout notification_action_tombstone 0x7f0a0004
int layout notification_template_custom_big 0x7f0a0005
int layout notification_template_icon_group 0x7f0a0006
int layout notification_template_part_chronometer 0x7f0a0007
int layout notification_template_part_time 0x7f0a0008
int layout splash_screen_view 0x7f0a0009
int mipmap app_banner 0x7f0b0000
int mipmap ic_launcher 0x7f0b0001
int mipmap ic_launcher_round 0x7f0b0002
int string ad 0x7f0c0000
int string androidx_startup 0x7f0c0001
int string app_name 0x7f0c0002
int string brand_logo_text 0x7f0c0003
int string budget 0x7f0c0004
int string call_notification_answer_action 0x7f0c0005
int string call_notification_answer_video_action 0x7f0c0006
int string call_notification_decline_action 0x7f0c0007
int string call_notification_hang_up_action 0x7f0c0008
int string call_notification_incoming_text 0x7f0c0009
int string call_notification_ongoing_text 0x7f0c000a
int string call_notification_screening_text 0x7f0c000b
int string cast_and_crew 0x7f0c000c
int string close_drawer 0x7f0c000d
int string close_sheet 0x7f0c000e
int string default_error_message 0x7f0c000f
int string default_popup_window_title 0x7f0c0010
int string delete_account_dialog_text 0x7f0c0011
int string delete_account_dialog_title 0x7f0c0012
int string director 0x7f0c0013
int string dropdown_menu 0x7f0c0014
int string exo_download_completed 0x7f0c0015
int string exo_download_description 0x7f0c0016
int string exo_download_downloading 0x7f0c0017
int string exo_download_failed 0x7f0c0018
int string exo_download_notification_channel_name 0x7f0c0019
int string exo_download_paused 0x7f0c001a
int string exo_download_paused_for_network 0x7f0c001b
int string exo_download_paused_for_wifi 0x7f0c001c
int string exo_download_removing 0x7f0c001d
int string favorites_added_last_week 0x7f0c001e
int string favorites_available_in_4k 0x7f0c001f
int string favorites_movies 0x7f0c0020
int string favorites_tv_shows 0x7f0c0021
int string favorites_unknown 0x7f0c0022
int string generic_error_msg 0x7f0c0023
int string in_progress 0x7f0c0024
int string indeterminate 0x7f0c0025
int string language_section_listItem_icon_content_description 0x7f0c0026
int string live 0x7f0c0027
int string m3c_bottom_sheet_collapse_description 0x7f0c0028
int string m3c_bottom_sheet_dismiss_description 0x7f0c0029
int string m3c_bottom_sheet_drag_handle_description 0x7f0c002a
int string m3c_bottom_sheet_expand_description 0x7f0c002b
int string m3c_bottom_sheet_pane_title 0x7f0c002c
int string m3c_date_input_headline 0x7f0c002d
int string m3c_date_input_headline_description 0x7f0c002e
int string m3c_date_input_invalid_for_pattern 0x7f0c002f
int string m3c_date_input_invalid_not_allowed 0x7f0c0030
int string m3c_date_input_invalid_year_range 0x7f0c0031
int string m3c_date_input_label 0x7f0c0032
int string m3c_date_input_no_input_description 0x7f0c0033
int string m3c_date_input_title 0x7f0c0034
int string m3c_date_picker_headline 0x7f0c0035
int string m3c_date_picker_headline_description 0x7f0c0036
int string m3c_date_picker_navigate_to_year_description 0x7f0c0037
int string m3c_date_picker_no_selection_description 0x7f0c0038
int string m3c_date_picker_scroll_to_earlier_years 0x7f0c0039
int string m3c_date_picker_scroll_to_later_years 0x7f0c003a
int string m3c_date_picker_switch_to_calendar_mode 0x7f0c003b
int string m3c_date_picker_switch_to_day_selection 0x7f0c003c
int string m3c_date_picker_switch_to_input_mode 0x7f0c003d
int string m3c_date_picker_switch_to_next_month 0x7f0c003e
int string m3c_date_picker_switch_to_previous_month 0x7f0c003f
int string m3c_date_picker_switch_to_year_selection 0x7f0c0040
int string m3c_date_picker_title 0x7f0c0041
int string m3c_date_picker_today_description 0x7f0c0042
int string m3c_date_picker_year_picker_pane_title 0x7f0c0043
int string m3c_date_range_input_invalid_range_input 0x7f0c0044
int string m3c_date_range_input_title 0x7f0c0045
int string m3c_date_range_picker_day_in_range 0x7f0c0046
int string m3c_date_range_picker_end_headline 0x7f0c0047
int string m3c_date_range_picker_scroll_to_next_month 0x7f0c0048
int string m3c_date_range_picker_scroll_to_previous_month 0x7f0c0049
int string m3c_date_range_picker_start_headline 0x7f0c004a
int string m3c_date_range_picker_title 0x7f0c004b
int string m3c_dialog 0x7f0c004c
int string m3c_dropdown_menu_collapsed 0x7f0c004d
int string m3c_dropdown_menu_expanded 0x7f0c004e
int string m3c_dropdown_menu_toggle 0x7f0c004f
int string m3c_search_bar_search 0x7f0c0050
int string m3c_snackbar_dismiss 0x7f0c0051
int string m3c_suggestions_available 0x7f0c0052
int string m3c_time_picker_am 0x7f0c0053
int string m3c_time_picker_hour 0x7f0c0054
int string m3c_time_picker_hour_24h_suffix 0x7f0c0055
int string m3c_time_picker_hour_selection 0x7f0c0056
int string m3c_time_picker_hour_suffix 0x7f0c0057
int string m3c_time_picker_hour_text_field 0x7f0c0058
int string m3c_time_picker_minute 0x7f0c0059
int string m3c_time_picker_minute_selection 0x7f0c005a
int string m3c_time_picker_minute_suffix 0x7f0c005b
int string m3c_time_picker_minute_text_field 0x7f0c005c
int string m3c_time_picker_period_toggle_description 0x7f0c005d
int string m3c_time_picker_pm 0x7f0c005e
int string m3c_tooltip_long_press_label 0x7f0c005f
int string m3c_tooltip_pane_description 0x7f0c0060
int string message_error 0x7f0c0061
int string message_loading 0x7f0c0062
int string movie_category_desc 0x7f0c0063
int string music 0x7f0c0064
int string navigation_menu 0x7f0c0065
int string no_keep_it 0x7f0c0066
int string not_selected 0x7f0c0067
int string original_language 0x7f0c0068
int string profile_screen_listItem_icon_content_description 0x7f0c0069
int string range_end 0x7f0c006a
int string range_start 0x7f0c006b
int string revenue 0x7f0c006c
int string reviews 0x7f0c006d
int string screenplay 0x7f0c006e
int string search_screen_et_placeholder 0x7f0c006f
int string selected 0x7f0c0070
int string state_empty 0x7f0c0071
int string state_off 0x7f0c0072
int string state_on 0x7f0c0073
int string status 0x7f0c0074
int string status_bar_notification_info_overflow 0x7f0c0075
int string switch_role 0x7f0c0076
int string tab 0x7f0c0077
int string template_percent 0x7f0c0078
int string tooltip_description 0x7f0c0079
int string tooltip_label 0x7f0c007a
int string top_10_movies_title 0x7f0c007b
int string watch_now 0x7f0c007c
int string watch_trailer 0x7f0c007d
int string yes_delete_account 0x7f0c007e
int style Base_Theme_SplashScreen 0x7f0d0000
int style Base_Theme_SplashScreen_DayNight 0x7f0d0001
int style Base_Theme_SplashScreen_Light 0x7f0d0002
int style Base_v21_Theme_SplashScreen 0x7f0d0003
int style Base_v21_Theme_SplashScreen_Light 0x7f0d0004
int style Base_v27_Theme_SplashScreen 0x7f0d0005
int style Base_v27_Theme_SplashScreen_Light 0x7f0d0006
int style DialogWindowTheme 0x7f0d0007
int style EdgeToEdgeFloatingDialogTheme 0x7f0d0008
int style EdgeToEdgeFloatingDialogWindowTheme 0x7f0d0009
int style FloatingDialogTheme 0x7f0d000a
int style FloatingDialogWindowTheme 0x7f0d000b
int style TextAppearance_Compat_Notification 0x7f0d000c
int style TextAppearance_Compat_Notification_Info 0x7f0d000d
int style TextAppearance_Compat_Notification_Line2 0x7f0d000e
int style TextAppearance_Compat_Notification_Time 0x7f0d000f
int style TextAppearance_Compat_Notification_Title 0x7f0d0010
int style Theme_App_Starting 0x7f0d0011
int style Theme_JetStream 0x7f0d0012
int style Theme_SplashScreen 0x7f0d0013
int style Theme_SplashScreen_Common 0x7f0d0014
int style Theme_SplashScreen_IconBackground 0x7f0d0015
int style Widget_Compat_NotificationActionContainer 0x7f0d0016
int style Widget_Compat_NotificationActionText 0x7f0d0017
int[] styleable ActivityNavigator { 0x01010003, 0x7f030000, 0x7f030003, 0x7f030004, 0x7f030027 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable Capability { 0x7f030021, 0x7f030024 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030001, 0x7f030015 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030010 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030008, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030028 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable NavAction { 0x010100d0, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030016, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030022 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f030002, 0x7f03001a }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030000, 0x7f030017, 0x7f030029 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f030026 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f030018 }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f030014 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f030023 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int xml backup_rules 0x7f0f0000
int xml data_extraction_rules 0x7f0f0001
