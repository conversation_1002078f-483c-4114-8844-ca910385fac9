{"logs": [{"outputFile": "com.google.jetstream-mergeDebugResources-66:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1deee87c461d53e27c7ec2397b1a8929\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "112,116", "startColumns": "4,4", "startOffsets": "6792,6969", "endColumns": "53,66", "endOffsets": "6841,7031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de8ad5933734fe01333c8ce03dbebdb6\\transformed\\navigation-runtime-2.8.8\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "115,351,538,541", "startColumns": "4,4,4,4", "startOffsets": "6916,22108,28721,28836", "endLines": "115,357,540,543", "endColumns": "52,24,24,24", "endOffsets": "6964,22407,28831,28946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d5b162717bed6dc35c1a21e4bad4638\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "8614", "endColumns": "82", "endOffsets": "8692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76081e3f0d6e0e3bdde7fa4601d62d99\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "2,9,10,13,14,19,20,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,150,151,152,153,154,155,156,266,306,307,311,312,316,349,350,390,396,406,441,471,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,605,677,851,916,1183,1252,2223,2293,2361,2433,2503,2564,2638,2711,2772,2833,2895,2959,3021,3082,3150,3250,3310,3376,3449,3518,3575,3627,4203,4275,4351,4416,4475,4534,4594,4654,4714,4774,4834,4894,4954,5014,5074,5134,5193,5253,5313,5373,5433,5493,5553,5613,5673,5733,5793,5852,5912,5972,6031,6090,6149,6208,6267,6846,6881,7143,7198,7261,7316,7374,7432,7493,7556,7613,7664,7714,7775,7832,7898,7932,7967,8510,8841,8908,8980,9049,9118,9192,9264,16920,19296,19413,19614,19724,19925,21969,22041,23299,23502,23803,25609,26609,27291", "endLines": "2,9,10,13,14,19,20,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,113,114,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,150,151,152,153,154,155,156,266,306,310,311,315,316,349,350,395,405,440,461,503,509", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,672,760,911,977,1247,1310,2288,2356,2428,2498,2559,2633,2706,2767,2828,2890,2954,3016,3077,3145,3245,3305,3371,3444,3513,3570,3622,3684,4270,4346,4411,4470,4529,4589,4649,4709,4769,4829,4889,4949,5009,5069,5129,5188,5248,5308,5368,5428,5488,5548,5608,5668,5728,5788,5847,5907,5967,6026,6085,6144,6203,6262,6321,6876,6911,7193,7256,7311,7369,7427,7488,7551,7608,7659,7709,7770,7827,7893,7927,7962,7997,8575,8903,8975,9044,9113,9187,9259,9347,16986,19408,19609,19719,19920,20049,22036,22103,23497,23798,25604,26285,27286,27453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2683060292804e831097c0bbf5535791\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "108,118,140,462,467", "startColumns": "4,4,4,4,4", "startOffsets": "6563,7078,8252,26290,26460", "endLines": "108,118,140,466,470", "endColumns": "56,64,63,24,24", "endOffsets": "6615,7138,8311,26455,26604"}}, {"source": "E:\\jetstrem-tvpayer\\jetstream\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "45,17,19,28,21,32,31,22,42,43,40,41,44,18,38,46,48,47,39,24,34,27,37,29,30,23,35,26,36,20,25,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2481,600,726,1182,831,1388,1315,878,2277,2348,2167,2220,2419,648,2001,2516,2609,2555,2100,976,1704,1117,1915,1225,1270,925,1756,1074,1849,782,1017,1633", "endColumns": "33,46,54,41,45,243,71,45,69,69,51,55,60,76,97,37,69,52,65,39,50,63,84,43,43,49,91,41,64,47,55,69", "endOffsets": "2510,642,776,1219,872,1627,1382,919,2342,2413,2214,2271,2475,720,2094,2549,2674,2603,2161,1011,1750,1176,1995,1264,1309,970,1843,1111,1909,825,1068,1698"}, "to": {"startLines": "145,147,148,149,157,162,163,164,175,176,177,178,179,180,183,184,246,247,248,249,251,253,254,257,258,259,260,265,272,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8580,8697,8744,8799,9352,9684,9928,10000,10750,10820,10890,10942,10998,11059,11248,11346,15825,15895,15948,16014,16116,16221,16285,16474,16518,16562,16612,16878,17275,17340,17388,17444", "endColumns": "33,46,54,41,45,243,71,45,69,69,51,55,60,76,97,37,69,52,65,39,50,63,84,43,43,49,91,41,64,47,55,69", "endOffsets": "8609,8739,8794,8836,9393,9923,9995,10041,10815,10885,10937,10993,11054,11131,11341,11379,15890,15943,16009,16049,16162,16280,16365,16513,16557,16607,16699,16915,17335,17383,17439,17509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\056f65275b6119073c278699d563b9b6\\transformed\\media3-exoplayer-1.6.0-beta01\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "166,167,168,169,170,171,172,173,174", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10104,10174,10236,10301,10365,10442,10507,10597,10681", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "10169,10231,10296,10360,10437,10502,10592,10676,10745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f4a5aa1c9ee769e0f52ab0d833391f6\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "3,4,5,6,7,8,62,63,64,65,66,67,68,142,276,277,278,279,281,323,332,345", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "210,270,329,398,470,533,3689,3763,3839,3915,3992,4063,4132,8369,17514,17595,17687,17780,17889,20461,20921,21696", "endLines": "3,4,5,6,7,8,62,63,64,65,66,67,68,142,276,277,278,280,282,331,344,348", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "265,324,393,465,528,600,3758,3834,3910,3987,4058,4127,4198,8432,17590,17682,17775,17884,18005,20916,21691,21964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\598ef20c77cd1b7c623fcd4d2eedf4e3\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "104,106,107,110,111,141,158,159,160,161,165,181,182,250,252,255,256,261,262,263,264,267,268,269,283,299,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6326,6450,6508,6686,6737,8316,9398,9463,9517,9583,10046,11136,11188,16054,16167,16370,16420,16704,16750,16796,16838,16991,17038,17074,18010,18990,19101", "endLines": "104,106,107,110,111,141,158,159,160,161,165,181,182,250,252,255,256,261,262,263,264,267,268,269,285,301,305", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "6395,6503,6558,6732,6787,8364,9458,9512,9578,9679,10099,11183,11243,16111,16216,16415,16469,16745,16791,16833,16873,17033,17069,17159,18117,19096,19291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3ff6a694fcbdd48a9d50b8a2d9e5cf55\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "143,185,186,187,188,189,190,191,192,193,194,197,198,199,200,201,202,203,204,205,206,207,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,286,296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8437,11384,11472,11558,11639,11723,11792,11857,11940,12046,12132,12252,12306,12375,12436,12505,12594,12689,12763,12860,12953,13051,13200,13291,13379,13475,13573,13637,13705,13792,13886,13953,14025,14097,14198,14307,14383,14452,14500,14566,14630,14704,14761,14818,14890,14940,14994,15065,15136,15206,15275,15333,15409,15480,15554,15640,15690,15760,18122,18837", "endLines": "143,185,186,187,188,189,190,191,192,193,196,197,198,199,200,201,202,203,204,205,206,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,295,298", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "8505,11467,11553,11634,11718,11787,11852,11935,12041,12127,12247,12301,12370,12431,12500,12589,12684,12758,12855,12948,13046,13195,13286,13374,13470,13568,13632,13700,13787,13881,13948,14020,14092,14193,14302,14378,14447,14495,14561,14625,14699,14756,14813,14885,14935,14989,15060,15131,15201,15270,15328,15404,15475,15549,15635,15685,15755,15820,18832,18985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67a3f5723098babf64b890147a256c9e\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "270,271", "startColumns": "4,4", "startOffsets": "17164,17220", "endColumns": "55,54", "endOffsets": "17215,17270"}}, {"source": "E:\\jetstrem-tvpayer\\jetstream\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "21,19", "startColumns": "4,4", "startOffsets": "732,642", "endLines": "25,19", "endColumns": "12,86", "endOffsets": "1052,724"}, "to": {"startLines": "317,322", "startColumns": "4,4", "startOffsets": "20054,20375", "endLines": "321,322", "endColumns": "12,85", "endOffsets": "20370,20456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cedd32b86dc8d6e4ba9ff279133a7a14\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "135", "startColumns": "4", "startOffsets": "8002", "endColumns": "42", "endOffsets": "8040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5da86a88389be1e494453e72f8f377b2\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "138", "startColumns": "4", "startOffsets": "8148", "endColumns": "53", "endOffsets": "8197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cc7ab9342e28d3a7a61e29019635d2d\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "358,374,380,549,565", "startColumns": "4,4,4,4,4", "startOffsets": "22412,22837,23015,29133,29544", "endLines": "373,379,389,564,568", "endColumns": "24,24,24,24,24", "endOffsets": "22832,23010,23294,29539,29666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\206c82f3be136f083be7fcbc2b65c7f8\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "6620", "endColumns": "65", "endOffsets": "6681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b114b0bd796e9060d848c372b0f76ef\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "8202", "endColumns": "49", "endOffsets": "8247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eb9ff48044364c3fad1cf2adadd08589\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "117,137", "startColumns": "4,4", "startOffsets": "7036,8088", "endColumns": "41,59", "endOffsets": "7073,8143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\150c03a8d21ab2a4eb785d5874481ac1\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6400", "endColumns": "49", "endOffsets": "6445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f9deb594537463bd032194fa21f2274e\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "136", "startColumns": "4", "startOffsets": "8045", "endColumns": "42", "endOffsets": "8083"}}, {"source": "E:\\jetstrem-tvpayer\\jetstream\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "31,41,37,39,42,18,32,38,40,20,22,24,26,34,36,28,30,19,21,23,25,33,35,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1290,1760,1574,1658,1802,640,1336,1615,1708,739,836,935,1036,1427,1522,1136,1235,696,784,890,982,1384,1472,1092,1182", "endColumns": "44,40,39,48,56,54,46,41,50,43,52,45,54,43,50,44,53,41,50,43,52,41,48,42,51", "endOffsets": "1330,1796,1609,1702,1854,690,1378,1652,1754,778,884,976,1086,1466,1568,1176,1284,733,830,929,1030,1421,1516,1130,1229"}, "to": {"startLines": "11,12,15,16,17,18,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "765,810,982,1022,1071,1128,1315,1362,1404,1455,1499,1552,1598,1653,1697,1748,1793,1847,1889,1940,1984,2037,2079,2128,2171", "endColumns": "44,40,39,48,56,54,46,41,50,43,52,45,54,43,50,44,53,41,50,43,52,41,48,42,51", "endOffsets": "805,846,1017,1066,1123,1178,1357,1399,1450,1494,1547,1593,1648,1692,1743,1788,1842,1884,1935,1979,2032,2074,2123,2166,2218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c27db03c4863491c9a7de21f025bc191\\transformed\\navigation-common-2.8.8\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "510,523,529,535,544", "startColumns": "4,4,4,4,4", "startOffsets": "27458,28097,28341,28588,28951", "endLines": "522,528,534,537,548", "endColumns": "24,24,24,24,24", "endOffsets": "28092,28336,28583,28716,29128"}}]}]}