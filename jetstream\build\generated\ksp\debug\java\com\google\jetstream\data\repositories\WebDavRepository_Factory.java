package com.google.jetstream.data.repositories;

import com.google.jetstream.data.database.dao.ResourceDirectoryDao;
import com.google.jetstream.data.database.dao.WebDavConfigDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class WebDavRepository_Factory implements Factory<WebDavRepository> {
  private final Provider<WebDavConfigDao> webDavConfigDaoProvider;

  private final Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider;

  public WebDavRepository_Factory(Provider<WebDavConfigDao> webDavConfigDaoProvider,
      Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider) {
    this.webDavConfigDaoProvider = webDavConfigDaoProvider;
    this.resourceDirectoryDaoProvider = resourceDirectoryDaoProvider;
  }

  @Override
  public WebDavRepository get() {
    return newInstance(webDavConfigDaoProvider.get(), resourceDirectoryDaoProvider.get());
  }

  public static WebDavRepository_Factory create(
      javax.inject.Provider<WebDavConfigDao> webDavConfigDaoProvider,
      javax.inject.Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider) {
    return new WebDavRepository_Factory(Providers.asDaggerProvider(webDavConfigDaoProvider), Providers.asDaggerProvider(resourceDirectoryDaoProvider));
  }

  public static WebDavRepository_Factory create(Provider<WebDavConfigDao> webDavConfigDaoProvider,
      Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider) {
    return new WebDavRepository_Factory(webDavConfigDaoProvider, resourceDirectoryDaoProvider);
  }

  public static WebDavRepository newInstance(WebDavConfigDao webDavConfigDao,
      ResourceDirectoryDao resourceDirectoryDao) {
    return new WebDavRepository(webDavConfigDao, resourceDirectoryDao);
  }
}
