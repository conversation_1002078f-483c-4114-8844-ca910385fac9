package com.google.jetstream.presentation.screens.profile;

import com.google.jetstream.data.repositories.WebDavRepository;
import com.google.jetstream.data.webdav.WebDavService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class WebDavBrowserViewModel_Factory implements Factory<WebDavBrowserViewModel> {
  private final Provider<WebDavRepository> repositoryProvider;

  private final Provider<WebDavService> webDavServiceProvider;

  public WebDavBrowserViewModel_Factory(Provider<WebDavRepository> repositoryProvider,
      Provider<WebDavService> webDavServiceProvider) {
    this.repositoryProvider = repositoryProvider;
    this.webDavServiceProvider = webDavServiceProvider;
  }

  @Override
  public WebDavBrowserViewModel get() {
    return newInstance(repositoryProvider.get(), webDavServiceProvider.get());
  }

  public static WebDavBrowserViewModel_Factory create(
      javax.inject.Provider<WebDavRepository> repositoryProvider,
      javax.inject.Provider<WebDavService> webDavServiceProvider) {
    return new WebDavBrowserViewModel_Factory(Providers.asDaggerProvider(repositoryProvider), Providers.asDaggerProvider(webDavServiceProvider));
  }

  public static WebDavBrowserViewModel_Factory create(Provider<WebDavRepository> repositoryProvider,
      Provider<WebDavService> webDavServiceProvider) {
    return new WebDavBrowserViewModel_Factory(repositoryProvider, webDavServiceProvider);
  }

  public static WebDavBrowserViewModel newInstance(WebDavRepository repository,
      WebDavService webDavService) {
    return new WebDavBrowserViewModel(repository, webDavService);
  }
}
