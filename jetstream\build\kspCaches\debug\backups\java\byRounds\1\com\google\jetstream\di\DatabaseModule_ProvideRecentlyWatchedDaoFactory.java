package com.google.jetstream.di;

import com.google.jetstream.data.database.JetStreamDatabase;
import com.google.jetstream.data.database.dao.RecentlyWatchedDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DatabaseModule_ProvideRecentlyWatchedDaoFactory implements Factory<RecentlyWatchedDao> {
  private final Provider<JetStreamDatabase> databaseProvider;

  public DatabaseModule_ProvideRecentlyWatchedDaoFactory(
      Provider<JetStreamDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public RecentlyWatchedDao get() {
    return provideRecentlyWatchedDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideRecentlyWatchedDaoFactory create(
      javax.inject.Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideRecentlyWatchedDaoFactory(Providers.asDaggerProvider(databaseProvider));
  }

  public static DatabaseModule_ProvideRecentlyWatchedDaoFactory create(
      Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideRecentlyWatchedDaoFactory(databaseProvider);
  }

  public static RecentlyWatchedDao provideRecentlyWatchedDao(JetStreamDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideRecentlyWatchedDao(database));
  }
}
