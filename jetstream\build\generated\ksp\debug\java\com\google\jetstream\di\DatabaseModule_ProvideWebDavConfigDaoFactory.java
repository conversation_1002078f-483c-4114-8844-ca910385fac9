package com.google.jetstream.di;

import com.google.jetstream.data.database.JetStreamDatabase;
import com.google.jetstream.data.database.dao.WebDavConfigDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DatabaseModule_ProvideWebDavConfigDaoFactory implements Factory<WebDavConfigDao> {
  private final Provider<JetStreamDatabase> databaseProvider;

  public DatabaseModule_ProvideWebDavConfigDaoFactory(
      Provider<JetStreamDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public WebDavConfigDao get() {
    return provideWebDavConfigDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideWebDavConfigDaoFactory create(
      javax.inject.Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideWebDavConfigDaoFactory(Providers.asDaggerProvider(databaseProvider));
  }

  public static DatabaseModule_ProvideWebDavConfigDaoFactory create(
      Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideWebDavConfigDaoFactory(databaseProvider);
  }

  public static WebDavConfigDao provideWebDavConfigDao(JetStreamDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideWebDavConfigDao(database));
  }
}
