package com.google.jetstream.presentation.screens.search;

import com.google.jetstream.data.repositories.MovieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SearchScreenViewModel_Factory implements Factory<SearchScreenViewModel> {
  private final Provider<MovieRepository> movieRepositoryProvider;

  public SearchScreenViewModel_Factory(Provider<MovieRepository> movieRepositoryProvider) {
    this.movieRepositoryProvider = movieRepositoryProvider;
  }

  @Override
  public SearchScreenViewModel get() {
    return newInstance(movieRepositoryProvider.get());
  }

  public static SearchScreenViewModel_Factory create(
      javax.inject.Provider<MovieRepository> movieRepositoryProvider) {
    return new SearchScreenViewModel_Factory(Providers.asDaggerProvider(movieRepositoryProvider));
  }

  public static SearchScreenViewModel_Factory create(
      Provider<MovieRepository> movieRepositoryProvider) {
    return new SearchScreenViewModel_Factory(movieRepositoryProvider);
  }

  public static SearchScreenViewModel newInstance(MovieRepository movieRepository) {
    return new SearchScreenViewModel(movieRepository);
  }
}
