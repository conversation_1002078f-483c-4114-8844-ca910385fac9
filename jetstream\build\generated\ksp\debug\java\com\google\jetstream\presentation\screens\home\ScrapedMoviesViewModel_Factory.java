package com.google.jetstream.presentation.screens.home;

import com.google.jetstream.data.repositories.ScrapedMoviesStore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ScrapedMoviesViewModel_Factory implements Factory<ScrapedMoviesViewModel> {
  private final Provider<ScrapedMoviesStore> storeProvider;

  public ScrapedMoviesViewModel_Factory(Provider<ScrapedMoviesStore> storeProvider) {
    this.storeProvider = storeProvider;
  }

  @Override
  public ScrapedMoviesViewModel get() {
    return newInstance(storeProvider.get());
  }

  public static ScrapedMoviesViewModel_Factory create(
      javax.inject.Provider<ScrapedMoviesStore> storeProvider) {
    return new ScrapedMoviesViewModel_Factory(Providers.asDaggerProvider(storeProvider));
  }

  public static ScrapedMoviesViewModel_Factory create(Provider<ScrapedMoviesStore> storeProvider) {
    return new ScrapedMoviesViewModel_Factory(storeProvider);
  }

  public static ScrapedMoviesViewModel newInstance(ScrapedMoviesStore store) {
    return new ScrapedMoviesViewModel(store);
  }
}
