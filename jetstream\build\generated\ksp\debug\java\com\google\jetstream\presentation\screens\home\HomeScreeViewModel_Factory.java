package com.google.jetstream.presentation.screens.home;

import com.google.jetstream.data.repositories.MovieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class HomeScreeViewModel_Factory implements Factory<HomeScreeViewModel> {
  private final Provider<MovieRepository> movieRepositoryProvider;

  public HomeScreeViewModel_Factory(Provider<MovieRepository> movieRepositoryProvider) {
    this.movieRepositoryProvider = movieRepositoryProvider;
  }

  @Override
  public HomeScreeViewModel get() {
    return newInstance(movieRepositoryProvider.get());
  }

  public static HomeScreeViewModel_Factory create(
      javax.inject.Provider<MovieRepository> movieRepositoryProvider) {
    return new HomeScreeViewModel_Factory(Providers.asDaggerProvider(movieRepositoryProvider));
  }

  public static HomeScreeViewModel_Factory create(
      Provider<MovieRepository> movieRepositoryProvider) {
    return new HomeScreeViewModel_Factory(movieRepositoryProvider);
  }

  public static HomeScreeViewModel newInstance(MovieRepository movieRepository) {
    return new HomeScreeViewModel(movieRepository);
  }
}
