package com.google.jetstream.data.repositories;

import com.google.jetstream.data.util.AssetsReader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MovieCategoryDataSource_Factory implements Factory<MovieCategoryDataSource> {
  private final Provider<AssetsReader> assetsReaderProvider;

  public MovieCategoryDataSource_Factory(Provider<AssetsReader> assetsReaderProvider) {
    this.assetsReaderProvider = assetsReaderProvider;
  }

  @Override
  public MovieCategoryDataSource get() {
    return newInstance(assetsReaderProvider.get());
  }

  public static MovieCategoryDataSource_Factory create(
      javax.inject.Provider<AssetsReader> assetsReaderProvider) {
    return new MovieCategoryDataSource_Factory(Providers.asDaggerProvider(assetsReaderProvider));
  }

  public static MovieCategoryDataSource_Factory create(
      Provider<AssetsReader> assetsReaderProvider) {
    return new MovieCategoryDataSource_Factory(assetsReaderProvider);
  }

  public static MovieCategoryDataSource newInstance(AssetsReader assetsReader) {
    return new MovieCategoryDataSource(assetsReader);
  }
}
