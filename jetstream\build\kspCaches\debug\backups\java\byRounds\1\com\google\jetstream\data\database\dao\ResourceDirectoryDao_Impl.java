package com.google.jetstream.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.google.jetstream.data.database.entities.ResourceDirectoryEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ResourceDirectoryDao_Impl implements ResourceDirectoryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ResourceDirectoryEntity> __insertionAdapterOfResourceDirectoryEntity;

  private final EntityDeletionOrUpdateAdapter<ResourceDirectoryEntity> __deletionAdapterOfResourceDirectoryEntity;

  private final EntityDeletionOrUpdateAdapter<ResourceDirectoryEntity> __updateAdapterOfResourceDirectoryEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteDirectoryById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteDirectoriesByConfigId;

  public ResourceDirectoryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfResourceDirectoryEntity = new EntityInsertionAdapter<ResourceDirectoryEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `resource_directories` (`id`,`name`,`path`,`webDavConfigId`,`serverName`,`createdAt`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ResourceDirectoryEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getPath());
        statement.bindString(4, entity.getWebDavConfigId());
        statement.bindString(5, entity.getServerName());
        statement.bindLong(6, entity.getCreatedAt());
      }
    };
    this.__deletionAdapterOfResourceDirectoryEntity = new EntityDeletionOrUpdateAdapter<ResourceDirectoryEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `resource_directories` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ResourceDirectoryEntity entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfResourceDirectoryEntity = new EntityDeletionOrUpdateAdapter<ResourceDirectoryEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `resource_directories` SET `id` = ?,`name` = ?,`path` = ?,`webDavConfigId` = ?,`serverName` = ?,`createdAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ResourceDirectoryEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getPath());
        statement.bindString(4, entity.getWebDavConfigId());
        statement.bindString(5, entity.getServerName());
        statement.bindLong(6, entity.getCreatedAt());
        statement.bindString(7, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteDirectoryById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM resource_directories WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteDirectoriesByConfigId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM resource_directories WHERE webDavConfigId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertDirectory(final ResourceDirectoryEntity directory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfResourceDirectoryEntity.insert(directory);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteDirectory(final ResourceDirectoryEntity directory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfResourceDirectoryEntity.handle(directory);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDirectory(final ResourceDirectoryEntity directory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfResourceDirectoryEntity.handle(directory);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteDirectoryById(final String id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteDirectoryById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteDirectoryById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteDirectoriesByConfigId(final String configId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteDirectoriesByConfigId.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, configId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteDirectoriesByConfigId.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ResourceDirectoryEntity>> getAllDirectories() {
    final String _sql = "SELECT * FROM resource_directories ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"resource_directories"}, new Callable<List<ResourceDirectoryEntity>>() {
      @Override
      @NonNull
      public List<ResourceDirectoryEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPath = CursorUtil.getColumnIndexOrThrow(_cursor, "path");
          final int _cursorIndexOfWebDavConfigId = CursorUtil.getColumnIndexOrThrow(_cursor, "webDavConfigId");
          final int _cursorIndexOfServerName = CursorUtil.getColumnIndexOrThrow(_cursor, "serverName");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<ResourceDirectoryEntity> _result = new ArrayList<ResourceDirectoryEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ResourceDirectoryEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpPath;
            _tmpPath = _cursor.getString(_cursorIndexOfPath);
            final String _tmpWebDavConfigId;
            _tmpWebDavConfigId = _cursor.getString(_cursorIndexOfWebDavConfigId);
            final String _tmpServerName;
            _tmpServerName = _cursor.getString(_cursorIndexOfServerName);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new ResourceDirectoryEntity(_tmpId,_tmpName,_tmpPath,_tmpWebDavConfigId,_tmpServerName,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getDirectoryById(final String id,
      final Continuation<? super ResourceDirectoryEntity> $completion) {
    final String _sql = "SELECT * FROM resource_directories WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ResourceDirectoryEntity>() {
      @Override
      @Nullable
      public ResourceDirectoryEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPath = CursorUtil.getColumnIndexOrThrow(_cursor, "path");
          final int _cursorIndexOfWebDavConfigId = CursorUtil.getColumnIndexOrThrow(_cursor, "webDavConfigId");
          final int _cursorIndexOfServerName = CursorUtil.getColumnIndexOrThrow(_cursor, "serverName");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final ResourceDirectoryEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpPath;
            _tmpPath = _cursor.getString(_cursorIndexOfPath);
            final String _tmpWebDavConfigId;
            _tmpWebDavConfigId = _cursor.getString(_cursorIndexOfWebDavConfigId);
            final String _tmpServerName;
            _tmpServerName = _cursor.getString(_cursorIndexOfServerName);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result = new ResourceDirectoryEntity(_tmpId,_tmpName,_tmpPath,_tmpWebDavConfigId,_tmpServerName,_tmpCreatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDirectoriesByConfigId(final String configId,
      final Continuation<? super List<ResourceDirectoryEntity>> $completion) {
    final String _sql = "SELECT * FROM resource_directories WHERE webDavConfigId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, configId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ResourceDirectoryEntity>>() {
      @Override
      @NonNull
      public List<ResourceDirectoryEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPath = CursorUtil.getColumnIndexOrThrow(_cursor, "path");
          final int _cursorIndexOfWebDavConfigId = CursorUtil.getColumnIndexOrThrow(_cursor, "webDavConfigId");
          final int _cursorIndexOfServerName = CursorUtil.getColumnIndexOrThrow(_cursor, "serverName");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<ResourceDirectoryEntity> _result = new ArrayList<ResourceDirectoryEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ResourceDirectoryEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpPath;
            _tmpPath = _cursor.getString(_cursorIndexOfPath);
            final String _tmpWebDavConfigId;
            _tmpWebDavConfigId = _cursor.getString(_cursorIndexOfWebDavConfigId);
            final String _tmpServerName;
            _tmpServerName = _cursor.getString(_cursorIndexOfServerName);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new ResourceDirectoryEntity(_tmpId,_tmpName,_tmpPath,_tmpWebDavConfigId,_tmpServerName,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
