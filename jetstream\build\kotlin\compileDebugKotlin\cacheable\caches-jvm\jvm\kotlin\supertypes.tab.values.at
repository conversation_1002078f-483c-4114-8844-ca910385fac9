/ Header Record For PersistentHashMapValueStorage android.app.Application$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer7 6com.google.jetstream.data.repositories.MovieRepository3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum. -com.google.jetstream.data.webdav.WebDavResult. -com.google.jetstream.data.webdav.WebDavResult. -com.google.jetstream.data.webdav.WebDavResult kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModelM Lcom.google.jetstream.presentation.screens.categories.CategoriesScreenUiStateM Lcom.google.jetstream.presentation.screens.categories.CategoriesScreenUiState androidx.lifecycle.ViewModelT Scom.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiStateT Scom.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiStateT Scom.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState androidx.lifecycle.ViewModel3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer androidx.lifecycle.ViewModelL Kcom.google.jetstream.presentation.screens.favourites.FavouriteScreenUiStateL Kcom.google.jetstream.presentation.screens.favourites.FavouriteScreenUiState kotlin.Enum androidx.lifecycle.ViewModelA @com.google.jetstream.presentation.screens.home.HomeScreenUiStateA @com.google.jetstream.presentation.screens.home.HomeScreenUiStateA @com.google.jetstream.presentation.screens.home.HomeScreenUiState androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModelK Jcom.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiStateK Jcom.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiStateK Jcom.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiStateA @com.google.jetstream.presentation.screens.movies.EpisodesUiStateA @com.google.jetstream.presentation.screens.movies.EpisodesUiStateA @com.google.jetstream.presentation.screens.movies.EpisodesUiState androidx.lifecycle.ViewModelE Dcom.google.jetstream.presentation.screens.movies.MoviesScreenUiStateE Dcom.google.jetstream.presentation.screens.movies.MoviesScreenUiStateO Ncom.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiStateO Ncom.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiStateO Ncom.google.jetstream.presentation.screens.movietype.MovieTypeListScreenUiState androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel= <com.google.jetstream.presentation.screens.search.SearchState= <com.google.jetstream.presentation.screens.search.SearchState androidx.lifecycle.ViewModelB Acom.google.jetstream.presentation.screens.shows.ShowScreenUiStateB Acom.google.jetstream.presentation.screens.shows.ShowScreenUiState androidx.lifecycle.ViewModelO Ncom.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiStateO Ncom.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiStateO Ncom.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum