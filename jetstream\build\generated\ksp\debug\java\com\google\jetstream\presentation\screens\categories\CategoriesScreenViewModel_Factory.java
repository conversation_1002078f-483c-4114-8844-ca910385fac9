package com.google.jetstream.presentation.screens.categories;

import com.google.jetstream.data.repositories.MovieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CategoriesScreenViewModel_Factory implements Factory<CategoriesScreenViewModel> {
  private final Provider<MovieRepository> movieRepositoryProvider;

  public CategoriesScreenViewModel_Factory(Provider<MovieRepository> movieRepositoryProvider) {
    this.movieRepositoryProvider = movieRepositoryProvider;
  }

  @Override
  public CategoriesScreenViewModel get() {
    return newInstance(movieRepositoryProvider.get());
  }

  public static CategoriesScreenViewModel_Factory create(
      javax.inject.Provider<MovieRepository> movieRepositoryProvider) {
    return new CategoriesScreenViewModel_Factory(Providers.asDaggerProvider(movieRepositoryProvider));
  }

  public static CategoriesScreenViewModel_Factory create(
      Provider<MovieRepository> movieRepositoryProvider) {
    return new CategoriesScreenViewModel_Factory(movieRepositoryProvider);
  }

  public static CategoriesScreenViewModel newInstance(MovieRepository movieRepository) {
    return new CategoriesScreenViewModel(movieRepository);
  }
}
