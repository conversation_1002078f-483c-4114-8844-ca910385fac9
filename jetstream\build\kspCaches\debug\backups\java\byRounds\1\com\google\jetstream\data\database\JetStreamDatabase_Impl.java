package com.google.jetstream.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.google.jetstream.data.database.dao.RecentlyWatchedDao;
import com.google.jetstream.data.database.dao.RecentlyWatchedDao_Impl;
import com.google.jetstream.data.database.dao.ResourceDirectoryDao;
import com.google.jetstream.data.database.dao.ResourceDirectoryDao_Impl;
import com.google.jetstream.data.database.dao.ScrapedItemDao;
import com.google.jetstream.data.database.dao.ScrapedItemDao_Impl;
import com.google.jetstream.data.database.dao.WebDavConfigDao;
import com.google.jetstream.data.database.dao.WebDavConfigDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class JetStreamDatabase_Impl extends JetStreamDatabase {
  private volatile WebDavConfigDao _webDavConfigDao;

  private volatile ResourceDirectoryDao _resourceDirectoryDao;

  private volatile ScrapedItemDao _scrapedItemDao;

  private volatile RecentlyWatchedDao _recentlyWatchedDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(8) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `webdav_configs` (`id` TEXT NOT NULL, `displayName` TEXT NOT NULL, `serverUrl` TEXT NOT NULL, `username` TEXT NOT NULL, `password` TEXT NOT NULL, `isConnected` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `resource_directories` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `path` TEXT NOT NULL, `webDavConfigId` TEXT NOT NULL, `serverName` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `scraped_items` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `posterUri` TEXT NOT NULL, `releaseDate` TEXT, `rating` REAL, `type` TEXT NOT NULL, `sourcePath` TEXT, `createdAt` INTEGER NOT NULL, `backdropUri` TEXT, `pgRating` TEXT, `categories` TEXT, `duration` TEXT, `director` TEXT, `screenplay` TEXT, `music` TEXT, `castAndCrew` TEXT, `availableSeasons` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `recently_watched` (`movieId` TEXT NOT NULL, `movieTitle` TEXT NOT NULL, `backdropUri` TEXT NOT NULL, `posterUri` TEXT NOT NULL, `description` TEXT NOT NULL, `releaseDate` TEXT, `rating` REAL, `type` TEXT NOT NULL, `lastWatchedAt` INTEGER NOT NULL, `watchProgress` REAL, `currentPositionMs` INTEGER, `durationMs` INTEGER, `episodeId` TEXT, `episodeNumber` INTEGER, `seasonNumber` INTEGER, `episodeTitle` TEXT, PRIMARY KEY(`movieId`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '73112c39b9a7e0654e30a746d54c9ab0')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `webdav_configs`");
        db.execSQL("DROP TABLE IF EXISTS `resource_directories`");
        db.execSQL("DROP TABLE IF EXISTS `scraped_items`");
        db.execSQL("DROP TABLE IF EXISTS `recently_watched`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsWebdavConfigs = new HashMap<String, TableInfo.Column>(7);
        _columnsWebdavConfigs.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWebdavConfigs.put("displayName", new TableInfo.Column("displayName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWebdavConfigs.put("serverUrl", new TableInfo.Column("serverUrl", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWebdavConfigs.put("username", new TableInfo.Column("username", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWebdavConfigs.put("password", new TableInfo.Column("password", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWebdavConfigs.put("isConnected", new TableInfo.Column("isConnected", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWebdavConfigs.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysWebdavConfigs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesWebdavConfigs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoWebdavConfigs = new TableInfo("webdav_configs", _columnsWebdavConfigs, _foreignKeysWebdavConfigs, _indicesWebdavConfigs);
        final TableInfo _existingWebdavConfigs = TableInfo.read(db, "webdav_configs");
        if (!_infoWebdavConfigs.equals(_existingWebdavConfigs)) {
          return new RoomOpenHelper.ValidationResult(false, "webdav_configs(com.google.jetstream.data.database.entities.WebDavConfigEntity).\n"
                  + " Expected:\n" + _infoWebdavConfigs + "\n"
                  + " Found:\n" + _existingWebdavConfigs);
        }
        final HashMap<String, TableInfo.Column> _columnsResourceDirectories = new HashMap<String, TableInfo.Column>(6);
        _columnsResourceDirectories.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsResourceDirectories.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsResourceDirectories.put("path", new TableInfo.Column("path", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsResourceDirectories.put("webDavConfigId", new TableInfo.Column("webDavConfigId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsResourceDirectories.put("serverName", new TableInfo.Column("serverName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsResourceDirectories.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysResourceDirectories = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesResourceDirectories = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoResourceDirectories = new TableInfo("resource_directories", _columnsResourceDirectories, _foreignKeysResourceDirectories, _indicesResourceDirectories);
        final TableInfo _existingResourceDirectories = TableInfo.read(db, "resource_directories");
        if (!_infoResourceDirectories.equals(_existingResourceDirectories)) {
          return new RoomOpenHelper.ValidationResult(false, "resource_directories(com.google.jetstream.data.database.entities.ResourceDirectoryEntity).\n"
                  + " Expected:\n" + _infoResourceDirectories + "\n"
                  + " Found:\n" + _existingResourceDirectories);
        }
        final HashMap<String, TableInfo.Column> _columnsScrapedItems = new HashMap<String, TableInfo.Column>(18);
        _columnsScrapedItems.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("posterUri", new TableInfo.Column("posterUri", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("releaseDate", new TableInfo.Column("releaseDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("rating", new TableInfo.Column("rating", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("sourcePath", new TableInfo.Column("sourcePath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("backdropUri", new TableInfo.Column("backdropUri", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("pgRating", new TableInfo.Column("pgRating", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("categories", new TableInfo.Column("categories", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("duration", new TableInfo.Column("duration", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("director", new TableInfo.Column("director", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("screenplay", new TableInfo.Column("screenplay", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("music", new TableInfo.Column("music", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("castAndCrew", new TableInfo.Column("castAndCrew", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsScrapedItems.put("availableSeasons", new TableInfo.Column("availableSeasons", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysScrapedItems = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesScrapedItems = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoScrapedItems = new TableInfo("scraped_items", _columnsScrapedItems, _foreignKeysScrapedItems, _indicesScrapedItems);
        final TableInfo _existingScrapedItems = TableInfo.read(db, "scraped_items");
        if (!_infoScrapedItems.equals(_existingScrapedItems)) {
          return new RoomOpenHelper.ValidationResult(false, "scraped_items(com.google.jetstream.data.database.entities.ScrapedItemEntity).\n"
                  + " Expected:\n" + _infoScrapedItems + "\n"
                  + " Found:\n" + _existingScrapedItems);
        }
        final HashMap<String, TableInfo.Column> _columnsRecentlyWatched = new HashMap<String, TableInfo.Column>(16);
        _columnsRecentlyWatched.put("movieId", new TableInfo.Column("movieId", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("movieTitle", new TableInfo.Column("movieTitle", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("backdropUri", new TableInfo.Column("backdropUri", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("posterUri", new TableInfo.Column("posterUri", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("releaseDate", new TableInfo.Column("releaseDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("rating", new TableInfo.Column("rating", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("lastWatchedAt", new TableInfo.Column("lastWatchedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("watchProgress", new TableInfo.Column("watchProgress", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("currentPositionMs", new TableInfo.Column("currentPositionMs", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("durationMs", new TableInfo.Column("durationMs", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("episodeId", new TableInfo.Column("episodeId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("episodeNumber", new TableInfo.Column("episodeNumber", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("seasonNumber", new TableInfo.Column("seasonNumber", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsRecentlyWatched.put("episodeTitle", new TableInfo.Column("episodeTitle", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysRecentlyWatched = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesRecentlyWatched = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoRecentlyWatched = new TableInfo("recently_watched", _columnsRecentlyWatched, _foreignKeysRecentlyWatched, _indicesRecentlyWatched);
        final TableInfo _existingRecentlyWatched = TableInfo.read(db, "recently_watched");
        if (!_infoRecentlyWatched.equals(_existingRecentlyWatched)) {
          return new RoomOpenHelper.ValidationResult(false, "recently_watched(com.google.jetstream.data.database.entities.RecentlyWatchedEntity).\n"
                  + " Expected:\n" + _infoRecentlyWatched + "\n"
                  + " Found:\n" + _existingRecentlyWatched);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "73112c39b9a7e0654e30a746d54c9ab0", "23265068e4de4c75b8ff6137635b7865");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "webdav_configs","resource_directories","scraped_items","recently_watched");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `webdav_configs`");
      _db.execSQL("DELETE FROM `resource_directories`");
      _db.execSQL("DELETE FROM `scraped_items`");
      _db.execSQL("DELETE FROM `recently_watched`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(WebDavConfigDao.class, WebDavConfigDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ResourceDirectoryDao.class, ResourceDirectoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ScrapedItemDao.class, ScrapedItemDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(RecentlyWatchedDao.class, RecentlyWatchedDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public WebDavConfigDao webDavConfigDao() {
    if (_webDavConfigDao != null) {
      return _webDavConfigDao;
    } else {
      synchronized(this) {
        if(_webDavConfigDao == null) {
          _webDavConfigDao = new WebDavConfigDao_Impl(this);
        }
        return _webDavConfigDao;
      }
    }
  }

  @Override
  public ResourceDirectoryDao resourceDirectoryDao() {
    if (_resourceDirectoryDao != null) {
      return _resourceDirectoryDao;
    } else {
      synchronized(this) {
        if(_resourceDirectoryDao == null) {
          _resourceDirectoryDao = new ResourceDirectoryDao_Impl(this);
        }
        return _resourceDirectoryDao;
      }
    }
  }

  @Override
  public ScrapedItemDao scrapedItemDao() {
    if (_scrapedItemDao != null) {
      return _scrapedItemDao;
    } else {
      synchronized(this) {
        if(_scrapedItemDao == null) {
          _scrapedItemDao = new ScrapedItemDao_Impl(this);
        }
        return _scrapedItemDao;
      }
    }
  }

  @Override
  public RecentlyWatchedDao recentlyWatchedDao() {
    if (_recentlyWatchedDao != null) {
      return _recentlyWatchedDao;
    } else {
      synchronized(this) {
        if(_recentlyWatchedDao == null) {
          _recentlyWatchedDao = new RecentlyWatchedDao_Impl(this);
        }
        return _recentlyWatchedDao;
      }
    }
  }
}
