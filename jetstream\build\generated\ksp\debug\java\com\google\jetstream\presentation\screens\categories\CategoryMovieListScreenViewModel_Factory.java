package com.google.jetstream.presentation.screens.categories;

import androidx.lifecycle.SavedStateHandle;
import com.google.jetstream.data.repositories.MovieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CategoryMovieListScreenViewModel_Factory implements Factory<CategoryMovieListScreenViewModel> {
  private final Provider<SavedStateHandle> savedStateHandleProvider;

  private final Provider<MovieRepository> movieRepositoryProvider;

  public CategoryMovieListScreenViewModel_Factory(
      Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<MovieRepository> movieRepositoryProvider) {
    this.savedStateHandleProvider = savedStateHandleProvider;
    this.movieRepositoryProvider = movieRepositoryProvider;
  }

  @Override
  public CategoryMovieListScreenViewModel get() {
    return newInstance(savedStateHandleProvider.get(), movieRepositoryProvider.get());
  }

  public static CategoryMovieListScreenViewModel_Factory create(
      javax.inject.Provider<SavedStateHandle> savedStateHandleProvider,
      javax.inject.Provider<MovieRepository> movieRepositoryProvider) {
    return new CategoryMovieListScreenViewModel_Factory(Providers.asDaggerProvider(savedStateHandleProvider), Providers.asDaggerProvider(movieRepositoryProvider));
  }

  public static CategoryMovieListScreenViewModel_Factory create(
      Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<MovieRepository> movieRepositoryProvider) {
    return new CategoryMovieListScreenViewModel_Factory(savedStateHandleProvider, movieRepositoryProvider);
  }

  public static CategoryMovieListScreenViewModel newInstance(SavedStateHandle savedStateHandle,
      MovieRepository movieRepository) {
    return new CategoryMovieListScreenViewModel(savedStateHandle, movieRepository);
  }
}
