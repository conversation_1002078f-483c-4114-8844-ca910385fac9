package com.google.jetstream.presentation.screens.movietype;

import androidx.lifecycle.SavedStateHandle;
import com.google.jetstream.data.repositories.ScrapedMoviesStore;
import com.google.jetstream.data.repositories.ScrapedTvStore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MovieTypeListScreenViewModel_Factory implements Factory<MovieTypeListScreenViewModel> {
  private final Provider<SavedStateHandle> savedStateHandleProvider;

  private final Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider;

  private final Provider<ScrapedTvStore> scrapedTvStoreProvider;

  public MovieTypeListScreenViewModel_Factory(Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      Provider<ScrapedTvStore> scrapedTvStoreProvider) {
    this.savedStateHandleProvider = savedStateHandleProvider;
    this.scrapedMoviesStoreProvider = scrapedMoviesStoreProvider;
    this.scrapedTvStoreProvider = scrapedTvStoreProvider;
  }

  @Override
  public MovieTypeListScreenViewModel get() {
    return newInstance(savedStateHandleProvider.get(), scrapedMoviesStoreProvider.get(), scrapedTvStoreProvider.get());
  }

  public static MovieTypeListScreenViewModel_Factory create(
      javax.inject.Provider<SavedStateHandle> savedStateHandleProvider,
      javax.inject.Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      javax.inject.Provider<ScrapedTvStore> scrapedTvStoreProvider) {
    return new MovieTypeListScreenViewModel_Factory(Providers.asDaggerProvider(savedStateHandleProvider), Providers.asDaggerProvider(scrapedMoviesStoreProvider), Providers.asDaggerProvider(scrapedTvStoreProvider));
  }

  public static MovieTypeListScreenViewModel_Factory create(
      Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      Provider<ScrapedTvStore> scrapedTvStoreProvider) {
    return new MovieTypeListScreenViewModel_Factory(savedStateHandleProvider, scrapedMoviesStoreProvider, scrapedTvStoreProvider);
  }

  public static MovieTypeListScreenViewModel newInstance(SavedStateHandle savedStateHandle,
      ScrapedMoviesStore scrapedMoviesStore, ScrapedTvStore scrapedTvStore) {
    return new MovieTypeListScreenViewModel(savedStateHandle, scrapedMoviesStore, scrapedTvStore);
  }
}
