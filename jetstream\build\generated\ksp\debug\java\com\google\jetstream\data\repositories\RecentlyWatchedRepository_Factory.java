package com.google.jetstream.data.repositories;

import com.google.jetstream.data.database.dao.RecentlyWatchedDao;
import com.google.jetstream.data.services.EpisodeMatchingService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class RecentlyWatchedRepository_Factory implements Factory<RecentlyWatchedRepository> {
  private final Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider;

  private final Provider<EpisodeMatchingService> episodeMatchingServiceProvider;

  public RecentlyWatchedRepository_Factory(Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider,
      Provider<EpisodeMatchingService> episodeMatchingServiceProvider) {
    this.recentlyWatchedDaoProvider = recentlyWatchedDaoProvider;
    this.episodeMatchingServiceProvider = episodeMatchingServiceProvider;
  }

  @Override
  public RecentlyWatchedRepository get() {
    return newInstance(recentlyWatchedDaoProvider.get(), episodeMatchingServiceProvider.get());
  }

  public static RecentlyWatchedRepository_Factory create(
      javax.inject.Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider,
      javax.inject.Provider<EpisodeMatchingService> episodeMatchingServiceProvider) {
    return new RecentlyWatchedRepository_Factory(Providers.asDaggerProvider(recentlyWatchedDaoProvider), Providers.asDaggerProvider(episodeMatchingServiceProvider));
  }

  public static RecentlyWatchedRepository_Factory create(
      Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider,
      Provider<EpisodeMatchingService> episodeMatchingServiceProvider) {
    return new RecentlyWatchedRepository_Factory(recentlyWatchedDaoProvider, episodeMatchingServiceProvider);
  }

  public static RecentlyWatchedRepository newInstance(RecentlyWatchedDao recentlyWatchedDao,
      EpisodeMatchingService episodeMatchingService) {
    return new RecentlyWatchedRepository(recentlyWatchedDao, episodeMatchingService);
  }
}
