package com.google.jetstream.presentation.screens.dashboard;

import com.google.jetstream.data.database.dao.ResourceDirectoryDao;
import com.google.jetstream.data.database.dao.ScrapedItemDao;
import com.google.jetstream.data.database.dao.WebDavConfigDao;
import com.google.jetstream.data.repositories.ScrapedMoviesStore;
import com.google.jetstream.data.repositories.ScrapedTvStore;
import com.google.jetstream.data.webdav.WebDavService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DashboardViewModel_Factory implements Factory<DashboardViewModel> {
  private final Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider;

  private final Provider<WebDavConfigDao> webDavConfigDaoProvider;

  private final Provider<WebDavService> webDavServiceProvider;

  private final Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider;

  private final Provider<ScrapedTvStore> scrapedTvStoreProvider;

  private final Provider<ScrapedItemDao> scrapedItemDaoProvider;

  public DashboardViewModel_Factory(Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider,
      Provider<WebDavConfigDao> webDavConfigDaoProvider,
      Provider<WebDavService> webDavServiceProvider,
      Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      Provider<ScrapedTvStore> scrapedTvStoreProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    this.resourceDirectoryDaoProvider = resourceDirectoryDaoProvider;
    this.webDavConfigDaoProvider = webDavConfigDaoProvider;
    this.webDavServiceProvider = webDavServiceProvider;
    this.scrapedMoviesStoreProvider = scrapedMoviesStoreProvider;
    this.scrapedTvStoreProvider = scrapedTvStoreProvider;
    this.scrapedItemDaoProvider = scrapedItemDaoProvider;
  }

  @Override
  public DashboardViewModel get() {
    return newInstance(resourceDirectoryDaoProvider.get(), webDavConfigDaoProvider.get(), webDavServiceProvider.get(), scrapedMoviesStoreProvider.get(), scrapedTvStoreProvider.get(), scrapedItemDaoProvider.get());
  }

  public static DashboardViewModel_Factory create(
      javax.inject.Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider,
      javax.inject.Provider<WebDavConfigDao> webDavConfigDaoProvider,
      javax.inject.Provider<WebDavService> webDavServiceProvider,
      javax.inject.Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      javax.inject.Provider<ScrapedTvStore> scrapedTvStoreProvider,
      javax.inject.Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    return new DashboardViewModel_Factory(Providers.asDaggerProvider(resourceDirectoryDaoProvider), Providers.asDaggerProvider(webDavConfigDaoProvider), Providers.asDaggerProvider(webDavServiceProvider), Providers.asDaggerProvider(scrapedMoviesStoreProvider), Providers.asDaggerProvider(scrapedTvStoreProvider), Providers.asDaggerProvider(scrapedItemDaoProvider));
  }

  public static DashboardViewModel_Factory create(
      Provider<ResourceDirectoryDao> resourceDirectoryDaoProvider,
      Provider<WebDavConfigDao> webDavConfigDaoProvider,
      Provider<WebDavService> webDavServiceProvider,
      Provider<ScrapedMoviesStore> scrapedMoviesStoreProvider,
      Provider<ScrapedTvStore> scrapedTvStoreProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    return new DashboardViewModel_Factory(resourceDirectoryDaoProvider, webDavConfigDaoProvider, webDavServiceProvider, scrapedMoviesStoreProvider, scrapedTvStoreProvider, scrapedItemDaoProvider);
  }

  public static DashboardViewModel newInstance(ResourceDirectoryDao resourceDirectoryDao,
      WebDavConfigDao webDavConfigDao, WebDavService webDavService,
      ScrapedMoviesStore scrapedMoviesStore, ScrapedTvStore scrapedTvStore,
      ScrapedItemDao scrapedItemDao) {
    return new DashboardViewModel(resourceDirectoryDao, webDavConfigDao, webDavService, scrapedMoviesStore, scrapedTvStore, scrapedItemDao);
  }
}
