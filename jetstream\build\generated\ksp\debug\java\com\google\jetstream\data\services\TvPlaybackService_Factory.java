package com.google.jetstream.data.services;

import com.google.jetstream.data.database.dao.RecentlyWatchedDao;
import com.google.jetstream.data.database.dao.ScrapedItemDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class TvPlaybackService_Factory implements Factory<TvPlaybackService> {
  private final Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider;

  private final Provider<EpisodeMatchingService> episodeMatchingServiceProvider;

  private final Provider<ScrapedItemDao> scrapedItemDaoProvider;

  public TvPlaybackService_Factory(Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider,
      Provider<EpisodeMatchingService> episodeMatchingServiceProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    this.recentlyWatchedDaoProvider = recentlyWatchedDaoProvider;
    this.episodeMatchingServiceProvider = episodeMatchingServiceProvider;
    this.scrapedItemDaoProvider = scrapedItemDaoProvider;
  }

  @Override
  public TvPlaybackService get() {
    return newInstance(recentlyWatchedDaoProvider.get(), episodeMatchingServiceProvider.get(), scrapedItemDaoProvider.get());
  }

  public static TvPlaybackService_Factory create(
      javax.inject.Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider,
      javax.inject.Provider<EpisodeMatchingService> episodeMatchingServiceProvider,
      javax.inject.Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    return new TvPlaybackService_Factory(Providers.asDaggerProvider(recentlyWatchedDaoProvider), Providers.asDaggerProvider(episodeMatchingServiceProvider), Providers.asDaggerProvider(scrapedItemDaoProvider));
  }

  public static TvPlaybackService_Factory create(
      Provider<RecentlyWatchedDao> recentlyWatchedDaoProvider,
      Provider<EpisodeMatchingService> episodeMatchingServiceProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider) {
    return new TvPlaybackService_Factory(recentlyWatchedDaoProvider, episodeMatchingServiceProvider, scrapedItemDaoProvider);
  }

  public static TvPlaybackService newInstance(RecentlyWatchedDao recentlyWatchedDao,
      EpisodeMatchingService episodeMatchingService, ScrapedItemDao scrapedItemDao) {
    return new TvPlaybackService(recentlyWatchedDao, episodeMatchingService, scrapedItemDao);
  }
}
