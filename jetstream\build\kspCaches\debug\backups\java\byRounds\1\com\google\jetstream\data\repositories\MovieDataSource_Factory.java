package com.google.jetstream.data.repositories;

import com.google.jetstream.data.util.AssetsReader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MovieDataSource_Factory implements Factory<MovieDataSource> {
  private final Provider<AssetsReader> assetsReaderProvider;

  public MovieDataSource_Factory(Provider<AssetsReader> assetsReaderProvider) {
    this.assetsReaderProvider = assetsReaderProvider;
  }

  @Override
  public MovieDataSource get() {
    return newInstance(assetsReaderProvider.get());
  }

  public static MovieDataSource_Factory create(
      javax.inject.Provider<AssetsReader> assetsReaderProvider) {
    return new MovieDataSource_Factory(Providers.asDaggerProvider(assetsReaderProvider));
  }

  public static MovieDataSource_Factory create(Provider<AssetsReader> assetsReaderProvider) {
    return new MovieDataSource_Factory(assetsReaderProvider);
  }

  public static MovieDataSource newInstance(AssetsReader assetsReader) {
    return new MovieDataSource(assetsReader);
  }
}
