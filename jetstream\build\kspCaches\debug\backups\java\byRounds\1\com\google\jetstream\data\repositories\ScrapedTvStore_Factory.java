package com.google.jetstream.data.repositories;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ScrapedTvStore_Factory implements Factory<ScrapedTvStore> {
  @Override
  public ScrapedTvStore get() {
    return newInstance();
  }

  public static ScrapedTvStore_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ScrapedTvStore newInstance() {
    return new ScrapedTvStore();
  }

  private static final class InstanceHolder {
    static final ScrapedTvStore_Factory INSTANCE = new ScrapedTvStore_Factory();
  }
}
