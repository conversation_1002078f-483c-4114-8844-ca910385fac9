package com.google.jetstream.presentation.screens.videoPlayer;

import androidx.lifecycle.SavedStateHandle;
import com.google.jetstream.data.database.dao.ScrapedItemDao;
import com.google.jetstream.data.database.dao.WebDavConfigDao;
import com.google.jetstream.data.repositories.MovieRepository;
import com.google.jetstream.data.repositories.RecentlyWatchedRepository;
import com.google.jetstream.data.services.TvPlaybackService;
import com.google.jetstream.data.webdav.WebDavService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class VideoPlayerScreenViewModel_Factory implements Factory<VideoPlayerScreenViewModel> {
  private final Provider<SavedStateHandle> savedStateHandleProvider;

  private final Provider<MovieRepository> repositoryProvider;

  private final Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider;

  private final Provider<ScrapedItemDao> scrapedItemDaoProvider;

  private final Provider<WebDavService> webDavServiceProvider;

  private final Provider<WebDavConfigDao> webDavConfigDaoProvider;

  private final Provider<TvPlaybackService> tvPlaybackServiceProvider;

  public VideoPlayerScreenViewModel_Factory(Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<MovieRepository> repositoryProvider,
      Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider,
      Provider<WebDavService> webDavServiceProvider,
      Provider<WebDavConfigDao> webDavConfigDaoProvider,
      Provider<TvPlaybackService> tvPlaybackServiceProvider) {
    this.savedStateHandleProvider = savedStateHandleProvider;
    this.repositoryProvider = repositoryProvider;
    this.recentlyWatchedRepositoryProvider = recentlyWatchedRepositoryProvider;
    this.scrapedItemDaoProvider = scrapedItemDaoProvider;
    this.webDavServiceProvider = webDavServiceProvider;
    this.webDavConfigDaoProvider = webDavConfigDaoProvider;
    this.tvPlaybackServiceProvider = tvPlaybackServiceProvider;
  }

  @Override
  public VideoPlayerScreenViewModel get() {
    return newInstance(savedStateHandleProvider.get(), repositoryProvider.get(), recentlyWatchedRepositoryProvider.get(), scrapedItemDaoProvider.get(), webDavServiceProvider.get(), webDavConfigDaoProvider.get(), tvPlaybackServiceProvider.get());
  }

  public static VideoPlayerScreenViewModel_Factory create(
      javax.inject.Provider<SavedStateHandle> savedStateHandleProvider,
      javax.inject.Provider<MovieRepository> repositoryProvider,
      javax.inject.Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider,
      javax.inject.Provider<ScrapedItemDao> scrapedItemDaoProvider,
      javax.inject.Provider<WebDavService> webDavServiceProvider,
      javax.inject.Provider<WebDavConfigDao> webDavConfigDaoProvider,
      javax.inject.Provider<TvPlaybackService> tvPlaybackServiceProvider) {
    return new VideoPlayerScreenViewModel_Factory(Providers.asDaggerProvider(savedStateHandleProvider), Providers.asDaggerProvider(repositoryProvider), Providers.asDaggerProvider(recentlyWatchedRepositoryProvider), Providers.asDaggerProvider(scrapedItemDaoProvider), Providers.asDaggerProvider(webDavServiceProvider), Providers.asDaggerProvider(webDavConfigDaoProvider), Providers.asDaggerProvider(tvPlaybackServiceProvider));
  }

  public static VideoPlayerScreenViewModel_Factory create(
      Provider<SavedStateHandle> savedStateHandleProvider,
      Provider<MovieRepository> repositoryProvider,
      Provider<RecentlyWatchedRepository> recentlyWatchedRepositoryProvider,
      Provider<ScrapedItemDao> scrapedItemDaoProvider,
      Provider<WebDavService> webDavServiceProvider,
      Provider<WebDavConfigDao> webDavConfigDaoProvider,
      Provider<TvPlaybackService> tvPlaybackServiceProvider) {
    return new VideoPlayerScreenViewModel_Factory(savedStateHandleProvider, repositoryProvider, recentlyWatchedRepositoryProvider, scrapedItemDaoProvider, webDavServiceProvider, webDavConfigDaoProvider, tvPlaybackServiceProvider);
  }

  public static VideoPlayerScreenViewModel newInstance(SavedStateHandle savedStateHandle,
      MovieRepository repository, RecentlyWatchedRepository recentlyWatchedRepository,
      ScrapedItemDao scrapedItemDao, WebDavService webDavService, WebDavConfigDao webDavConfigDao,
      TvPlaybackService tvPlaybackService) {
    return new VideoPlayerScreenViewModel(savedStateHandle, repository, recentlyWatchedRepository, scrapedItemDao, webDavService, webDavConfigDao, tvPlaybackService);
  }
}
