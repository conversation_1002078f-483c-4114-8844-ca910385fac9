package com.google.jetstream.di;

import com.google.jetstream.data.database.JetStreamDatabase;
import com.google.jetstream.data.database.dao.ScrapedItemDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DatabaseModule_ProvideScrapedItemDaoFactory implements Factory<ScrapedItemDao> {
  private final Provider<JetStreamDatabase> databaseProvider;

  public DatabaseModule_ProvideScrapedItemDaoFactory(Provider<JetStreamDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ScrapedItemDao get() {
    return provideScrapedItemDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideScrapedItemDaoFactory create(
      javax.inject.Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideScrapedItemDaoFactory(Providers.asDaggerProvider(databaseProvider));
  }

  public static DatabaseModule_ProvideScrapedItemDaoFactory create(
      Provider<JetStreamDatabase> databaseProvider) {
    return new DatabaseModule_ProvideScrapedItemDaoFactory(databaseProvider);
  }

  public static ScrapedItemDao provideScrapedItemDao(JetStreamDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideScrapedItemDao(database));
  }
}
