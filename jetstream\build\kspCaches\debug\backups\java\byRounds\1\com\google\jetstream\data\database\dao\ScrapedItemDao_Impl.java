package com.google.jetstream.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.google.jetstream.data.database.entities.ScrapedItemEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ScrapedItemDao_Impl implements ScrapedItemDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ScrapedItemEntity> __insertionAdapterOfScrapedItemEntity;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  public ScrapedItemDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfScrapedItemEntity = new EntityInsertionAdapter<ScrapedItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `scraped_items` (`id`,`title`,`description`,`posterUri`,`releaseDate`,`rating`,`type`,`sourcePath`,`createdAt`,`backdropUri`,`pgRating`,`categories`,`duration`,`director`,`screenplay`,`music`,`castAndCrew`,`availableSeasons`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ScrapedItemEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getDescription());
        statement.bindString(4, entity.getPosterUri());
        if (entity.getReleaseDate() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getReleaseDate());
        }
        if (entity.getRating() == null) {
          statement.bindNull(6);
        } else {
          statement.bindDouble(6, entity.getRating());
        }
        statement.bindString(7, entity.getType());
        if (entity.getSourcePath() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getSourcePath());
        }
        statement.bindLong(9, entity.getCreatedAt());
        if (entity.getBackdropUri() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getBackdropUri());
        }
        if (entity.getPgRating() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getPgRating());
        }
        if (entity.getCategories() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getCategories());
        }
        if (entity.getDuration() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getDuration());
        }
        if (entity.getDirector() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getDirector());
        }
        if (entity.getScreenplay() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getScreenplay());
        }
        if (entity.getMusic() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getMusic());
        }
        if (entity.getCastAndCrew() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getCastAndCrew());
        }
        if (entity.getAvailableSeasons() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getAvailableSeasons());
        }
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM scraped_items";
        return _query;
      }
    };
  }

  @Override
  public Object upsertAll(final List<ScrapedItemEntity> items,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfScrapedItemEntity.insert(items);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ScrapedItemEntity>> getAllByType(final String type) {
    final String _sql = "SELECT * FROM scraped_items WHERE type = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, type);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scraped_items"}, new Callable<List<ScrapedItemEntity>>() {
      @Override
      @NonNull
      public List<ScrapedItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPosterUri = CursorUtil.getColumnIndexOrThrow(_cursor, "posterUri");
          final int _cursorIndexOfReleaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "releaseDate");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfSourcePath = CursorUtil.getColumnIndexOrThrow(_cursor, "sourcePath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfBackdropUri = CursorUtil.getColumnIndexOrThrow(_cursor, "backdropUri");
          final int _cursorIndexOfPgRating = CursorUtil.getColumnIndexOrThrow(_cursor, "pgRating");
          final int _cursorIndexOfCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "categories");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfDirector = CursorUtil.getColumnIndexOrThrow(_cursor, "director");
          final int _cursorIndexOfScreenplay = CursorUtil.getColumnIndexOrThrow(_cursor, "screenplay");
          final int _cursorIndexOfMusic = CursorUtil.getColumnIndexOrThrow(_cursor, "music");
          final int _cursorIndexOfCastAndCrew = CursorUtil.getColumnIndexOrThrow(_cursor, "castAndCrew");
          final int _cursorIndexOfAvailableSeasons = CursorUtil.getColumnIndexOrThrow(_cursor, "availableSeasons");
          final List<ScrapedItemEntity> _result = new ArrayList<ScrapedItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ScrapedItemEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpPosterUri;
            _tmpPosterUri = _cursor.getString(_cursorIndexOfPosterUri);
            final String _tmpReleaseDate;
            if (_cursor.isNull(_cursorIndexOfReleaseDate)) {
              _tmpReleaseDate = null;
            } else {
              _tmpReleaseDate = _cursor.getString(_cursorIndexOfReleaseDate);
            }
            final Float _tmpRating;
            if (_cursor.isNull(_cursorIndexOfRating)) {
              _tmpRating = null;
            } else {
              _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            }
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpSourcePath;
            if (_cursor.isNull(_cursorIndexOfSourcePath)) {
              _tmpSourcePath = null;
            } else {
              _tmpSourcePath = _cursor.getString(_cursorIndexOfSourcePath);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpBackdropUri;
            if (_cursor.isNull(_cursorIndexOfBackdropUri)) {
              _tmpBackdropUri = null;
            } else {
              _tmpBackdropUri = _cursor.getString(_cursorIndexOfBackdropUri);
            }
            final String _tmpPgRating;
            if (_cursor.isNull(_cursorIndexOfPgRating)) {
              _tmpPgRating = null;
            } else {
              _tmpPgRating = _cursor.getString(_cursorIndexOfPgRating);
            }
            final String _tmpCategories;
            if (_cursor.isNull(_cursorIndexOfCategories)) {
              _tmpCategories = null;
            } else {
              _tmpCategories = _cursor.getString(_cursorIndexOfCategories);
            }
            final String _tmpDuration;
            if (_cursor.isNull(_cursorIndexOfDuration)) {
              _tmpDuration = null;
            } else {
              _tmpDuration = _cursor.getString(_cursorIndexOfDuration);
            }
            final String _tmpDirector;
            if (_cursor.isNull(_cursorIndexOfDirector)) {
              _tmpDirector = null;
            } else {
              _tmpDirector = _cursor.getString(_cursorIndexOfDirector);
            }
            final String _tmpScreenplay;
            if (_cursor.isNull(_cursorIndexOfScreenplay)) {
              _tmpScreenplay = null;
            } else {
              _tmpScreenplay = _cursor.getString(_cursorIndexOfScreenplay);
            }
            final String _tmpMusic;
            if (_cursor.isNull(_cursorIndexOfMusic)) {
              _tmpMusic = null;
            } else {
              _tmpMusic = _cursor.getString(_cursorIndexOfMusic);
            }
            final String _tmpCastAndCrew;
            if (_cursor.isNull(_cursorIndexOfCastAndCrew)) {
              _tmpCastAndCrew = null;
            } else {
              _tmpCastAndCrew = _cursor.getString(_cursorIndexOfCastAndCrew);
            }
            final String _tmpAvailableSeasons;
            if (_cursor.isNull(_cursorIndexOfAvailableSeasons)) {
              _tmpAvailableSeasons = null;
            } else {
              _tmpAvailableSeasons = _cursor.getString(_cursorIndexOfAvailableSeasons);
            }
            _item = new ScrapedItemEntity(_tmpId,_tmpTitle,_tmpDescription,_tmpPosterUri,_tmpReleaseDate,_tmpRating,_tmpType,_tmpSourcePath,_tmpCreatedAt,_tmpBackdropUri,_tmpPgRating,_tmpCategories,_tmpDuration,_tmpDirector,_tmpScreenplay,_tmpMusic,_tmpCastAndCrew,_tmpAvailableSeasons);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getById(final String id,
      final Continuation<? super ScrapedItemEntity> $completion) {
    final String _sql = "SELECT * FROM scraped_items WHERE id = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ScrapedItemEntity>() {
      @Override
      @Nullable
      public ScrapedItemEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPosterUri = CursorUtil.getColumnIndexOrThrow(_cursor, "posterUri");
          final int _cursorIndexOfReleaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "releaseDate");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfSourcePath = CursorUtil.getColumnIndexOrThrow(_cursor, "sourcePath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfBackdropUri = CursorUtil.getColumnIndexOrThrow(_cursor, "backdropUri");
          final int _cursorIndexOfPgRating = CursorUtil.getColumnIndexOrThrow(_cursor, "pgRating");
          final int _cursorIndexOfCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "categories");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfDirector = CursorUtil.getColumnIndexOrThrow(_cursor, "director");
          final int _cursorIndexOfScreenplay = CursorUtil.getColumnIndexOrThrow(_cursor, "screenplay");
          final int _cursorIndexOfMusic = CursorUtil.getColumnIndexOrThrow(_cursor, "music");
          final int _cursorIndexOfCastAndCrew = CursorUtil.getColumnIndexOrThrow(_cursor, "castAndCrew");
          final int _cursorIndexOfAvailableSeasons = CursorUtil.getColumnIndexOrThrow(_cursor, "availableSeasons");
          final ScrapedItemEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpPosterUri;
            _tmpPosterUri = _cursor.getString(_cursorIndexOfPosterUri);
            final String _tmpReleaseDate;
            if (_cursor.isNull(_cursorIndexOfReleaseDate)) {
              _tmpReleaseDate = null;
            } else {
              _tmpReleaseDate = _cursor.getString(_cursorIndexOfReleaseDate);
            }
            final Float _tmpRating;
            if (_cursor.isNull(_cursorIndexOfRating)) {
              _tmpRating = null;
            } else {
              _tmpRating = _cursor.getFloat(_cursorIndexOfRating);
            }
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpSourcePath;
            if (_cursor.isNull(_cursorIndexOfSourcePath)) {
              _tmpSourcePath = null;
            } else {
              _tmpSourcePath = _cursor.getString(_cursorIndexOfSourcePath);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpBackdropUri;
            if (_cursor.isNull(_cursorIndexOfBackdropUri)) {
              _tmpBackdropUri = null;
            } else {
              _tmpBackdropUri = _cursor.getString(_cursorIndexOfBackdropUri);
            }
            final String _tmpPgRating;
            if (_cursor.isNull(_cursorIndexOfPgRating)) {
              _tmpPgRating = null;
            } else {
              _tmpPgRating = _cursor.getString(_cursorIndexOfPgRating);
            }
            final String _tmpCategories;
            if (_cursor.isNull(_cursorIndexOfCategories)) {
              _tmpCategories = null;
            } else {
              _tmpCategories = _cursor.getString(_cursorIndexOfCategories);
            }
            final String _tmpDuration;
            if (_cursor.isNull(_cursorIndexOfDuration)) {
              _tmpDuration = null;
            } else {
              _tmpDuration = _cursor.getString(_cursorIndexOfDuration);
            }
            final String _tmpDirector;
            if (_cursor.isNull(_cursorIndexOfDirector)) {
              _tmpDirector = null;
            } else {
              _tmpDirector = _cursor.getString(_cursorIndexOfDirector);
            }
            final String _tmpScreenplay;
            if (_cursor.isNull(_cursorIndexOfScreenplay)) {
              _tmpScreenplay = null;
            } else {
              _tmpScreenplay = _cursor.getString(_cursorIndexOfScreenplay);
            }
            final String _tmpMusic;
            if (_cursor.isNull(_cursorIndexOfMusic)) {
              _tmpMusic = null;
            } else {
              _tmpMusic = _cursor.getString(_cursorIndexOfMusic);
            }
            final String _tmpCastAndCrew;
            if (_cursor.isNull(_cursorIndexOfCastAndCrew)) {
              _tmpCastAndCrew = null;
            } else {
              _tmpCastAndCrew = _cursor.getString(_cursorIndexOfCastAndCrew);
            }
            final String _tmpAvailableSeasons;
            if (_cursor.isNull(_cursorIndexOfAvailableSeasons)) {
              _tmpAvailableSeasons = null;
            } else {
              _tmpAvailableSeasons = _cursor.getString(_cursorIndexOfAvailableSeasons);
            }
            _result = new ScrapedItemEntity(_tmpId,_tmpTitle,_tmpDescription,_tmpPosterUri,_tmpReleaseDate,_tmpRating,_tmpType,_tmpSourcePath,_tmpCreatedAt,_tmpBackdropUri,_tmpPgRating,_tmpCategories,_tmpDuration,_tmpDirector,_tmpScreenplay,_tmpMusic,_tmpCastAndCrew,_tmpAvailableSeasons);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
