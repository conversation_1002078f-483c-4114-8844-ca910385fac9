package com.google.jetstream.data.util;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AssetsReader_Factory implements Factory<AssetsReader> {
  private final Provider<Context> contextProvider;

  public AssetsReader_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AssetsReader get() {
    return newInstance(contextProvider.get());
  }

  public static AssetsReader_Factory create(javax.inject.Provider<Context> contextProvider) {
    return new AssetsReader_Factory(Providers.asDaggerProvider(contextProvider));
  }

  public static AssetsReader_Factory create(Provider<Context> contextProvider) {
    return new AssetsReader_Factory(contextProvider);
  }

  public static AssetsReader newInstance(Context context) {
    return new AssetsReader(context);
  }
}
