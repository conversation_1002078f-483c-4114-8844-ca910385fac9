package com.google.jetstream.presentation.screens.webdav;

import com.google.jetstream.data.repositories.WebDavRepository;
import com.google.jetstream.data.webdav.WebDavService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class WebDavBrowserViewModel_Factory implements Factory<WebDavBrowserViewModel> {
  private final Provider<WebDavService> webDavServiceProvider;

  private final Provider<WebDavRepository> repositoryProvider;

  public WebDavBrowserViewModel_Factory(Provider<WebDavService> webDavServiceProvider,
      Provider<WebDavRepository> repositoryProvider) {
    this.webDavServiceProvider = webDavServiceProvider;
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public WebDavBrowserViewModel get() {
    return newInstance(webDavServiceProvider.get(), repositoryProvider.get());
  }

  public static WebDavBrowserViewModel_Factory create(
      javax.inject.Provider<WebDavService> webDavServiceProvider,
      javax.inject.Provider<WebDavRepository> repositoryProvider) {
    return new WebDavBrowserViewModel_Factory(Providers.asDaggerProvider(webDavServiceProvider), Providers.asDaggerProvider(repositoryProvider));
  }

  public static WebDavBrowserViewModel_Factory create(Provider<WebDavService> webDavServiceProvider,
      Provider<WebDavRepository> repositoryProvider) {
    return new WebDavBrowserViewModel_Factory(webDavServiceProvider, repositoryProvider);
  }

  public static WebDavBrowserViewModel newInstance(WebDavService webDavService,
      WebDavRepository repository) {
    return new WebDavBrowserViewModel(webDavService, repository);
  }
}
