package com.google.jetstream.presentation.screens.favourites;

import com.google.jetstream.data.repositories.MovieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class FavouriteScreenViewModel_Factory implements Factory<FavouriteScreenViewModel> {
  private final Provider<MovieRepository> movieRepositoryProvider;

  public FavouriteScreenViewModel_Factory(Provider<MovieRepository> movieRepositoryProvider) {
    this.movieRepositoryProvider = movieRepositoryProvider;
  }

  @Override
  public FavouriteScreenViewModel get() {
    return newInstance(movieRepositoryProvider.get());
  }

  public static FavouriteScreenViewModel_Factory create(
      javax.inject.Provider<MovieRepository> movieRepositoryProvider) {
    return new FavouriteScreenViewModel_Factory(Providers.asDaggerProvider(movieRepositoryProvider));
  }

  public static FavouriteScreenViewModel_Factory create(
      Provider<MovieRepository> movieRepositoryProvider) {
    return new FavouriteScreenViewModel_Factory(movieRepositoryProvider);
  }

  public static FavouriteScreenViewModel newInstance(MovieRepository movieRepository) {
    return new FavouriteScreenViewModel(movieRepository);
  }
}
