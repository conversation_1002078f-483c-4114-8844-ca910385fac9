package com.google.jetstream.presentation.screens.shows;

import com.google.jetstream.data.repositories.MovieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ShowScreenViewModel_Factory implements Factory<ShowScreenViewModel> {
  private final Provider<MovieRepository> movieRepositoryProvider;

  public ShowScreenViewModel_Factory(Provider<MovieRepository> movieRepositoryProvider) {
    this.movieRepositoryProvider = movieRepositoryProvider;
  }

  @Override
  public ShowScreenViewModel get() {
    return newInstance(movieRepositoryProvider.get());
  }

  public static ShowScreenViewModel_Factory create(
      javax.inject.Provider<MovieRepository> movieRepositoryProvider) {
    return new ShowScreenViewModel_Factory(Providers.asDaggerProvider(movieRepositoryProvider));
  }

  public static ShowScreenViewModel_Factory create(
      Provider<MovieRepository> movieRepositoryProvider) {
    return new ShowScreenViewModel_Factory(movieRepositoryProvider);
  }

  public static ShowScreenViewModel newInstance(MovieRepository movieRepository) {
    return new ShowScreenViewModel(movieRepository);
  }
}
